package com.dragon.clicker.config

import org.opencv.core.Rect

data class Point(
    var x: Int,
    var y: Int,
)

data class TemplateMatchResult(
    val templatePath: String,
    val point: Point,
)

data class TemplateConfig(
    val path: String,
    val rect: Rect,
    var scale: Double = 1.0,
)

object ApiConstant {
    /***** Ratio *****/
    const val RATIO_IMAGE_BIGGEST = 0.9
    const val RATIO_IMAGE_BIG = 0.9
    const val RATIO_IMAGE_MID = 0.8

    /***** Time *****/
    const val CHECK_IMAGE_TIME_BIGGEST = 40_000L
    const val CHECK_IMAGE_TIME_BIG = 20_000L
    const val CHECK_IMAGE_TIME_MID = 10_000L
    const val CHECK_IMAGE_TIME_SMALL = 2_500L
    const val CHECK_IMAGE_TIMEOUT = 2_500L
    const val DURATION_DEFAULT = 300L
    const val DURATION_CLICK_DEFAULT = 1L
    const val WAITING_APP = 3000L
    const val WAITING_LEVELUP = 3000L
    const val TIME_CREATE_FARM_MAX = 5 * 60 * 1000L // 5 Minutes
    const val TIME_RUNFARM_RELOOP = 5 * 60 * 1000L // 5 Minutes
    const val DELAY_SHOP = 60L

    /***** Path *****/
    const val IMG_PATH = "Dragon/Img"
    const val MANAGER_FARM_PATH = "Dragon/Farm"
    const val PHONE_PATH = "Img/SMG935F"

    /***** App *****/
    const val DONE = "DONE"
    const val FAILED = "FAILED"
    const val HAYDAY = "com.supercell.hayday"
    const val NAAPP = "com.dragon.clicker"
    const val DRAW_DISPLAY = false
    const val LOG_TAG = "NaApp"
    val SELECTION_TEST = listOf("Click", "DragAndDrop", "PinchOut")
    const val HAR = "HAR"
    const val GROW = "GROW"
    const val TYPE_ONE = 1
    const val TYPE_TWO = 2
    const val TYPE_THREE = 3
    const val TYPE_FOUR_FINGER_ONE = 4
    const val TYPE_FOUR_FINGER_TWO = 5
    const val TYPE_FOUR_FINGER_THREE = 6
    const val TYPE_FOUR_FINGER_FOUR = 7
    const val API_SUCCESS = 0
    const val API_ERROR = 1
    const val API_FAILED = 2
    const val API_STOP = 3
    const val API_AGAIN = 4
    const val API_NEXTSTEP = 5
    const val API_LEVELUP = 6
    const val API_CREATEFARM = 7
    const val API_HOMEGRAY = 8
    const val STEP_NEWFARM = 1
    const val STEP_BUYCHICKEN = 2
    const val STEP_LEVELUPTHREE = 3
    const val ACHIEVEMENTS_TYPE_PAINT = 1
    const val ACHIEVEMENTS_TYPE_RECEIVE = 2
    const val BREAD_TYPE_BUY = 1
    const val BREAD_TYPE_PROD = 2
    const val MOVE_TYPE_CREATEFARM = 1
    const val MOVE_TYPE_NORMAL = 2
    const val MANAGERFARM_TYPE_CHECK = 0
    const val MANAGERFARM_TYPE_CREATE = 1
    const val MANAGERFARM_TYPE_SAVE = 2
    const val MANAGERFARM_TYPE_OPEN = 3
    const val CHECKLEVEL_TYPE_CREAT = 0
    const val CHECKLEVEL_TYPE_RUN = 1
    val LOOP_WORKING = listOf(0,1)
    val LOOP_NOT_WORKING = 3
    /******************** CONFIG ********************/

    /***** Config Auto *****/
    const val HAYDAY_PRICE = 2 // 0 default, 1 min, 2 max
    const val MAXFARM = 10 //Farm run

    /***** CONFIG HAYDAY 640 x 360 (RESIZED FROM 1280 x 720) *****/
    /***** Point - ALL COORDINATES DIVIDED BY 2 AGAIN *****/
    val POINT_CAP_PERMIS = Point(90,200)
    val POINT_CONTROL_OPEN = Point(60,100)
    val POINT_ONHAYDAY = Point(332,287)
    val POINT_SHOP = listOf(Point(58, 135), Point(68, 151))    // From (116,270), (136,303)
    val POINT_SHOP5_BOX1 = Point(230, 143)      // From (460, 287)
    val POINT_SHOP5_BOX2 = Point(323, 143)      // From (647, 287)
    val POINT_SHOP5_BOX3 = Point(417, 143)      // From (835, 287)
    val POINT_SHOP5_BOX4 = Point(230, 237)      // From (460, 475)
    val POINT_SHOP5_BOX5 = Point(323, 237)      // From (647, 475)
    val POINT_SHOP_SILO = Point(86, 104)        // From (173, 208)
    val POINT_SHOP_BARN = Point(89, 172)        // From (178, 345)
    val POINT_SHOP_FIRST_ITEM = Point(186, 100) // From (372, 200)
    val POINT_SHOP_ITEM_UP = Point(542, 96)     // From (1085, 193)

    val POINT_SHOP_SELL = Point(476, 326)       // From (953, 653)
    val POINT_SHOP_CLOSE_DELAY = Point(582, 41) // From (1164, 82)
    val POINT_OPENDM_SHOPBOX = listOf(
        Point(411, 228),    // From (823,456)
        Point(463, 128),    // From (927,257)
        Point(447, 224),    // From (895,448)
        Point(509, 132)     // From (1018,264)
    )
    val POINT_SHOP_BOX1 = Point(137, 145)       // From (275,291)
    val POINT_SHOP_BOX2 = Point(137, 220)       // From (275,440)
    val POINT_SHOP_BOX3 = Point(229, 145)       // From (459,291)
    val POINT_SHOP_BOX4 = Point(229, 220)       // From (459,440)
    val POINT_SHOP_BOX5 = Point(326, 145)       // From (653,291)
    val POINT_SHOP_BOX6 = Point(326, 220)       // From (653,440)
    val POINT_SHOP_BOX7 = Point(432, 145)       // From (865,291)
    val POINT_SHOP_BOX8 = Point(416, 220)       // From (833,440)
    val POINT_SHOP_BOX9 = Point(505, 145)       // From (1011,291)
    val POINT_SHOP_BOX10 = Point(505, 220)      // From (1011,440)
    val POINT_ADS_SELL = Point(320, 253)        // From (641,506)
    val POINT_SILO_STORAGE = listOf(
        Point(417, 13),     // From (834,26)
        Point(412, 3)       // From (825,7)
    )
    val POINT_BARN_STORAGE = listOf(
        Point(438, 40),     // From (876,81)
        Point(474, 38)      // From (948,77)
    )
    val POINT_UP_STORAGE = listOf(
        Point(320, 308),    // From (640,617)
        Point(450, 192)     // From (901,385)
    )
    val POINT_PAINT_SILO = Point(343, 87)       // From (687,175)
    val POINT_PAINT_BARN = Point(343, 87)       // From (687,175)
    val POINT_PUT_YEAROLD_START = Point(210, 195)  // From (421, 391)
    val POINT_PUT_YEAROLD_END = Point(285, 195)    // From (571, 391)
    val POINT_CONFIRM_PUTNAME = Point(545, 299)     // From (1090, 598)
    val POINT_BUY_CHICKEN = Point(50, 98)           // From (100, 197)
    val POINT_MOVE_CHICKEN1 = Point(376, 144)       // From (752, 289)
    val POINT_MOVE_CHICKEN2 = Point(547, 105)       // From (1094, 211)
    val POINT_CLOSE_BUY = Point(351, 328)           // From (702, 656)
    val POINT_HAR_COOP = Point(-50, 32)             // From (-100, 65)
    val POINT_GROW_COOP = Point(-118, 98)           // From (-236, 197)
    val POINT_FREE_EGGS = Point(139 / 16, 0)        // From (139 / 8, 0) - divided by 16 now
    val POINT_FIELD = Point(306, 102)               // From (613, 205)
    val POINT_HAR = Point(-64, -8)                  // From (-129, -16)
    val POINT_GROW_WHEAT = Point(-20, -10)          // From (-41, -21)
    val POINT_GROW_WHEAT7 = Point(-30, 29)          // From (-98, 71)
    val POINT_GROW_CORN = Point(-63, -22)          // From (-98, 71)
    val POINT_GROW_SUGARCANE = Point(-145, 30)      // From (-290, 60)
    val POINT_FIELD_A = Point(337,35)              // From (696, 59)
    val POINT_FIELD_B = Point(507,146)              // From (931, 177)
    val POINT_FIELD_C = Point(278,262)             // From (696, 294)
    val POINT_FIELD_D = Point(89,160)              // From (461, 177)
//    val POINT_FIELD_A = Point(348,29)              // From (696, 59)
//    val POINT_FIELD_B = Point(465,73)              // From (931, 177)
//    val POINT_FIELD_C = Point(348,147)             // From (696, 294)
//    val POINT_FIELD_D = Point(230,88)              // From (461, 177)
    val POINT_NEXT_LEVELUP = Point(407, 332)        // From (815, 665)
    val POINT_ROAD = Point(313, 285)                // From (778, 673)
    val POINT_SHOPSPC = Point(33, 316)              // From (66, 633)
    val POINT_SHOPSPC_DECOR = Point(323, 255)       // From (647, 511)
    val POINT_SHOPSPC_DECOR_ALL = Point(25, 83)     // From (50, 166)
    val POINT_SHOPSPC_DECOR_STORED = Point(75, 83)  // From (150, 166)
    val POINT_SHOPSPC_CATERPILLAR = Point(149, 287) // From (299, 575)
    val POINT_SHOPSPC_CATERPILLAR_MOVE1 = Point(376, 144) // From (752, 289)
    val POINT_SHOPSPC_CATERPILLAR_MOVE2 = Point(182, 27)  // From (365, 55)
    val POINT_SHOPSPC_CATERPILLAR_MOVE3 = Point(163, 40)  // From (327, 81)
    val POINT_SHOPSPC_MACHINE = Point(328, 162)            // From (656, 324)
    val POINT_SHOPSPC_MACHINE_BREAD = Point(46, 96)        // From (93, 193)
    val POINT_SHOPSPC_MACHINE_BREAD_MOVE1 = Point(376, 144) // From (752, 289)
    val POINT_SHOPSPC_MACHINE_BREAD_MOVE2 = Point(164, 83)  // From (328, 167)
    val POINT_SHOPSPC_MACHINE_BREAD_MOVE3 = Point(164, 83)  // From (328, 167)
    val POINT_SHOPSPC_MACHINE_BREAD_BREAD = Point(148, 88)
    val POINT_SHOPSPC_ANIMALHOME = Point(330, 80)           // From (660, 161)
    val POINT_SHOPSPC_FIELD = Point(50, 101)                // From (100, 203)
    val POINT_SHOPSPC_ANIMALS = Point(332, 118)             // From (665, 237)
    val POINT_ACHIEVEMENTS = Point(365, 17)                 // From (730, 34)
    val POINT_ACHIEVEMENTS_PAINTBRUSH = Point(288, 86)      // From (577, 173)
    val POINT_ACHIEVEMENTS_RECEIVE = Point(291, 252)        // From (582, 504)
    val POINT_ACHIEVEMENTS_CLOSE = Point(558, 30)           // From (1117, 60)
    val POINT_TRUCK = listOf(Point(282, 47), Point(278, 77)) // From (564,94), (557,154)
    val POINT_TRUCK_RUNTASK = Point(533, 312)               // From (1066, 624)
    val POINT_CLOSE = POINT_ACHIEVEMENTS_CLOSE
    val POINT_CLOSE_SHOP = Point(535, 34)                   // From (1071, 68)
    val POINT_DISTANCE = Point(12, 6)                       // From (25, 12)
    val POINT_COOP = Point(231, 62)                         // From (463, 124)
    val POINT_SHOPFIRST = Point(82, 328)                    // From (164, 657)
    val POINT_TRUCK_STICK = Point(244, 345)                 // From (488, 691)
    val POINT_TAB_FRIEND = Point(625, 330)                  // From (1250, 660)
    val POINT_GREG = Point(218, 308)                        // From (437, 616)
    val POINT_GREG_RIGHT = Point(567,303)
    val POINT_RECONECT = Point(211,320)
    val POINT_ADS_TREE = listOf(Point(137,174), Point(153,179), Point(159,186), Point(152,168))
    val POINT_BACK_HOME = Point(35,321)

    /***** Rect - ALL COORDINATES DIVIDED BY 2 AGAIN *****/
    val RECT_COOP = Rect(146, 12, 162, 100)                 // From (293, 25, 325, 200)
    val RECT_FREE_EGGS = Rect(254, 54, 152, 80)             // From (508, 108, 305, 160)
    val RECT_FIELD = Rect(90, 10, 300, 180)                // From (210, 50, 905, 441)
    val RECT_ADS = Rect(194,30,369,296)
    val RECT_GROW = Rect(150, 50, 300, 175)                 // From (300, 100, 600, 350)
    val RECT_FULL_SILO = Rect(448, 6, 55, 45)               // From (896, 13, 110, 90)
    val RECT_STARTNEWFARM = Rect(381, 87, 44, 25)           // From (763, 174, 89, 50)
    val RECT_PUTNAME = Rect(135, 8, 355, 50)                // From (271, 16, 710, 100)
    val RECT_FREE_WHEAT = Rect(390, 139, 125, 125)          // From (780, 278, 250, 250)
    val RECT_FREE_WHEAT_DONE = Rect(178, 65, 150, 75)       // From (357, 131, 300, 150)
    val RECT_LEVELUP = Rect(212, 27, 221, 50)               // From (424, 54, 443, 100)
    val RECT_BUYCHICKEN = Rect(27, 122, 55, 100)            // From (55, 244, 111, 200)
    val RECT_BUYCHICKEN_DONE = Rect(4, 57, 32, 46)          // From (8, 114, 65, 92)
    val RECT_HOME = Rect(3, 285, 57, 65)                    // From (7, 570, 115, 131)
    val RECT_LEVEL = Rect(22, 10, 22, 27)                   // From (45, 20, 45, 55)
    val RECT_NEWFARM = Rect(156, 21, 318, 61)               // From (313, 42, 637, 122)
    val RECT_RECONNECT = Rect(210, 4, 218, 47)              // From (421, 9, 436, 94)
    val RECT_ACHIEVEMENTS = Rect(291, 0, 150, 87)           // From (582, 0, 300, 175)
    val RECT_STORAGE = Rect(379, 0, 140, 90)                // From (758, 0, 280, 180)
    val RECT_SPC_FIELD = Rect(70, 140, 25, 27)              // From (140, 281, 50, 55)
    val RECT_TRUCK1 = Rect(125, 119, 36, 31)                // From (251, 239, 73, 62)
    val RECT_TRUCK2 = Rect(227, 119, 36, 31)                // From (455, 239, 73, 62)
    val RECT_TRUCK3 = Rect(323, 119, 36, 31)                // From (646, 239, 73, 62)
    val RECT_CHECKFIELD = Rect(269, 57, 65, 75)             // From (538, 114, 131, 150)
    val RECT_KEYBOARD_HAYDAY = Rect(0, 160, 38, 36)         // From (0, 321, 77, 73)
    val RECT_OPENSHOP_FIRST = Rect(163, 82, 13, 13)         // From (327, 165, 27, 27)
    val RECT_LOADINGSHOP = Rect(163, 76, 13, 13)            // From (327, 152, 27, 27)
    val RECT_ITEMS1 = Rect(150, 65, 100, 65)
    val RECT_ITEMS2 = Rect(250, 65, 100, 65)
    val RECT_ITEMS3 = Rect(150, 130, 100, 65)
    val RECT_ITEMS4 = Rect(250, 130, 100, 65)
    val RECT_ITEMS5 = Rect(150, 195, 100, 65)
    val RECT_ITEMS6 = Rect(250, 195, 100, 65)
    val RECT_ITEMS7 = Rect(150, 260, 100, 65)
    val RECT_ITEMS8 = Rect(250, 260, 100, 65)
    val RECT_QUANTITY_STORAGE = Rect(356,30,91,33)
    val RECT_QUANTITY_ITEMS = Rect(30,20,60,35)
    /***** Image Templates - Using same paths but with resized rects *****/
    val SMG935F_PUTNAME = TemplateConfig("$PHONE_PATH/put_name.png", RECT_PUTNAME)
    val SMG935F_START_NEWFARM = TemplateConfig("$PHONE_PATH/start_newfarm.png", RECT_STARTNEWFARM)
    val SMG935F_COOP = TemplateConfig("$PHONE_PATH/coop.png", RECT_COOP)
    val SMG935F_FREE_EGGS = TemplateConfig("$PHONE_PATH/free_eggs.png", RECT_FREE_EGGS)
    val SMG935F_DONE_FREE_EGGS = TemplateConfig("$PHONE_PATH/done_free_eggs.png", RECT_FREE_EGGS)
    val SMG935F_FREE_WHEAT = TemplateConfig("$PHONE_PATH/free_wheat.png", RECT_FREE_WHEAT)
    val SMG935F_DONE_FREE_WHEAT =
        listOf(
            TemplateConfig("$PHONE_PATH/done_free_wheat1.png", RECT_FREE_WHEAT_DONE),
        )
    val SMG935F_LEVELUP = TemplateConfig("$PHONE_PATH/levelup.png", RECT_LEVELUP)
    val SMG935F_BUYCHICKEN = TemplateConfig("$PHONE_PATH/buy_chicken.png", RECT_BUYCHICKEN)
    val SMG935F_BUYCHICKEN_DONE = TemplateConfig("$PHONE_PATH/buy_chicken_done.png", RECT_BUYCHICKEN_DONE)
    val SMG935F_HOME = TemplateConfig("$PHONE_PATH/home.png", RECT_HOME)
    val SMG935F_HOME_GRAY = TemplateConfig("$PHONE_PATH/home_gray.png", RECT_HOME)
    val SMG935F_LEVEL1 = TemplateConfig("$PHONE_PATH/level1.png", RECT_LEVEL)
    val SMG935F_LEVEL2 = TemplateConfig("$PHONE_PATH/level2.png", RECT_LEVEL)
    val SMG935F_LEVEL3 = TemplateConfig("$PHONE_PATH/level3.png", RECT_LEVEL)
    val SMG935F_LEVEL4 = TemplateConfig("$PHONE_PATH/level4.png", RECT_LEVEL)
    val SMG935F_LEVEL5 = TemplateConfig("$PHONE_PATH/level5.png", RECT_LEVEL)
    val SMG935F_LEVEL6 = TemplateConfig("$PHONE_PATH/level6.png", RECT_LEVEL)
    val SMG935F_LEVEL7 = TemplateConfig("$PHONE_PATH/level7.png", RECT_LEVEL)
    val SMG935F_LEVEL8 = TemplateConfig("$PHONE_PATH/level8.png", RECT_LEVEL)
    val SMG935F_LEVEL9 = TemplateConfig("$PHONE_PATH/level9.png", RECT_LEVEL)
    val SMG935F_LEVEL10 = TemplateConfig("$PHONE_PATH/level10.png", RECT_LEVEL)
    val SMG935F_LEVEL11 = TemplateConfig("$PHONE_PATH/level11.png", RECT_LEVEL)
    val SMG935F_LEVEL12 = TemplateConfig("$PHONE_PATH/level12.png", RECT_LEVEL)
    val SMG935F_LEVEL13 = TemplateConfig("$PHONE_PATH/level13.png", RECT_LEVEL)
    val SMG935F_LEVEL14 = TemplateConfig("$PHONE_PATH/level14.png", RECT_LEVEL)
    val SMG935F_NEWFARM =
        listOf(
            TemplateConfig("$PHONE_PATH/newfarm6.png", RECT_NEWFARM),
        )
    val SMG935F_RECONNECT = TemplateConfig("$PHONE_PATH/reconnect.png", RECT_RECONNECT)
    val SMG935F_BUYSPC = TemplateConfig("$PHONE_PATH/BuySPC.png", RECT_SPC_FIELD)
    val SMG935F_BUYSPC_DONE = TemplateConfig("$PHONE_PATH/BuySPC_Done.png", RECT_SPC_FIELD)
    val SMG935F_TRUCKTASK1 = TemplateConfig("$PHONE_PATH/truck_task.png", RECT_TRUCK1)
    val SMG935F_TRUCKTASK2 = TemplateConfig("$PHONE_PATH/truck_task.png", RECT_TRUCK2)
    val SMG935F_TRUCKTASK3 = TemplateConfig("$PHONE_PATH/truck_task.png", RECT_TRUCK3)
    val SMG935F_CHECKFIELD = TemplateConfig("$PHONE_PATH/check_field.png", RECT_CHECKFIELD)
    val SMG935F_KEYBOARD_HAYDAY = TemplateConfig("$PHONE_PATH/keyboard_hayday.png", RECT_KEYBOARD_HAYDAY)
    val SMG935F_SHOPFIRST = TemplateConfig("$PHONE_PATH/shopfirst.png", RECT_OPENSHOP_FIRST)
    val SMG935F_LOADINGSHOP = TemplateConfig("$PHONE_PATH/loading_shop.png", RECT_LOADINGSHOP)
    val SMG935F_ICON_ADS_WHEAT = TemplateConfig("$PHONE_PATH/icon_ads_wheat.png", RECT_ADS)
    val SMG935F_SICKLE_TEMPLATE = TemplateConfig("$PHONE_PATH/sickle_template.png", RECT_FIELD)
    val SMG935F_SICKLE_MASK = TemplateConfig("$PHONE_PATH/sickle_mask.png", RECT_FIELD)
    val SMG935F_WHEAT_ICON_ZERO = TemplateConfig("$PHONE_PATH/wheat_icon_zero.png", RECT_FIELD)
    val SMG935F_WHEAT_ICON_ZERO_MASK = TemplateConfig("$PHONE_PATH/wheat_icon_zero_mask.png", RECT_FIELD)
    val SMG935F_ADS_WHEAT_ICON = TemplateConfig("$PHONE_PATH/ads_wheat_icon.png", RECT_ADS)
    val SMG935F_ADS_WHEAT_ICON_MASK = TemplateConfig("$PHONE_PATH/ads_wheat_icon_mask.png", RECT_ADS)

    /***** IMAGE *****/
    const val SMG935F_BOLT = "$PHONE_PATH/bolt.png"
    const val SMG935F_PLANK = "$PHONE_PATH/plank.png"
    const val SMG935F_DUCT = "$PHONE_PATH/duct.png"
    const val SMG935F_NAIL = "$PHONE_PATH/nail.png"
    const val SMG935F_SCREW = "$PHONE_PATH/screw.png"
    const val SMG935F_WOODPANEL = "$PHONE_PATH/woodPanel.png"

    const val SMG935F_DIGITS_0 = "$PHONE_PATH/digits_0.png"
    const val SMG935F_DIGITS_1 = "$PHONE_PATH/digits_1.png"
    const val SMG935F_DIGITS_2 = "$PHONE_PATH/digits_2.png"
    const val SMG935F_DIGITS_3 = "$PHONE_PATH/digits_3.png"
    const val SMG935F_DIGITS_4 = "$PHONE_PATH/digits_4.png"
    const val SMG935F_DIGITS_5 = "$PHONE_PATH/digits_5.png"
    const val SMG935F_DIGITS_6 = "$PHONE_PATH/digits_6.png"
    const val SMG935F_DIGITS_7 = "$PHONE_PATH/digits_7.png"
    const val SMG935F_DIGITS_8 = "$PHONE_PATH/digits_8.png"
    const val SMG935F_DIGITS_9 = "$PHONE_PATH/digits_9.png"
    const val SMG935F_DIGITS_0_MASK = "$PHONE_PATH/digits_0_mask.png"
    const val SMG935F_DIGITS_1_MASK = "$PHONE_PATH/digits_1_mask.png"
    const val SMG935F_DIGITS_2_MASK = "$PHONE_PATH/digits_2_mask.png"
    const val SMG935F_DIGITS_3_MASK = "$PHONE_PATH/digits_3_mask.png"
    const val SMG935F_DIGITS_4_MASK = "$PHONE_PATH/digits_4_mask.png"
    const val SMG935F_DIGITS_5_MASK = "$PHONE_PATH/digits_5_mask.png"
    const val SMG935F_DIGITS_6_MASK = "$PHONE_PATH/digits_6_mask.png"
    const val SMG935F_DIGITS_7_MASK = "$PHONE_PATH/digits_7_mask.png"
    const val SMG935F_DIGITS_8_MASK = "$PHONE_PATH/digits_8_mask.png"
    const val SMG935F_DIGITS_9_MASK = "$PHONE_PATH/digits_9_mask.png"
    const val SMG935F_DGIS_0 = "$PHONE_PATH/dgis_0.png"
    const val SMG935F_DGIS_1 = "$PHONE_PATH/dgis_1.png"
    const val SMG935F_DGIS_2 = "$PHONE_PATH/dgis_2.png"
    const val SMG935F_DGIS_3 = "$PHONE_PATH/dgis_3.png"
    const val SMG935F_DGIS_4 = "$PHONE_PATH/dgis_4.png"
    const val SMG935F_DGIS_5 = "$PHONE_PATH/dgis_5.png"
    const val SMG935F_DGIS_6 = "$PHONE_PATH/dgis_6.png"
    const val SMG935F_DGIS_7 = "$PHONE_PATH/dgis_7.png"
    const val SMG935F_DGIS_8 = "$PHONE_PATH/dgis_8.png"
    const val SMG935F_DGIS_9 = "$PHONE_PATH/dgis_9.png"
    const val SMG935F_DGIS_DELIMITER = "$PHONE_PATH/dgis_delimiter.png"
    const val SMG935F_DGIS_0_MASK = "$PHONE_PATH/dgis_0_mask.png"
    const val SMG935F_DGIS_1_MASK = "$PHONE_PATH/dgis_1_mask.png"
    const val SMG935F_DGIS_2_MASK = "$PHONE_PATH/dgis_2_mask.png"
    const val SMG935F_DGIS_3_MASK = "$PHONE_PATH/dgis_3_mask.png"
    const val SMG935F_DGIS_4_MASK = "$PHONE_PATH/dgis_4_mask.png"
    const val SMG935F_DGIS_5_MASK = "$PHONE_PATH/dgis_5_mask.png"
    const val SMG935F_DGIS_6_MASK = "$PHONE_PATH/dgis_6_mask.png"
    const val SMG935F_DGIS_7_MASK = "$PHONE_PATH/dgis_7_mask.png"
    const val SMG935F_DGIS_8_MASK = "$PHONE_PATH/dgis_8_mask.png"
    const val SMG935F_DGIS_9_MASK = "$PHONE_PATH/dgis_9_mask.png"
    const val SMG935F_DGIS_DELIMITER_MASK = "$PHONE_PATH/dgis_delimiter_mask.png"

    /***** DEVIATION *****/
    const val OFFSET_SHOPBOX = 15
    const val OFFSET_ITEMS = 10
    const val OFFSET_SMALL = 3
    const val OFFSET_MID = 5

}

object GlobalVariabel {
    /***** CONFIG HAYDAY - RESIZED FOR 640 x 360 *****/
    var screenWidthReal = 640                                // From 1280
    var screenHeightReal = 360                               // From 720
    var isHaydayRunning = false
    var levelHayday = 1
    var stepHayday = 0
    var coopPoint = Point(0, 0)
    var fieldPoint = Point(306, 102)                         // From Point(613, 205)
    var farmRunning = 1
    var isMainActivity = 0
    var timeNano = 0L
    var numberFarm = 0
    var timeCreateFarm = 0L
    var timeRunFarm = 0L
    var restartApp = 0

    /***** CONFIG UI *****/
    var point1 = Point(0, 0)
    var point2 = Point(0, 0)
    var point3 = Point(0, 0)
    var point4 = Point(0, 0)
    var selection = ""
}
