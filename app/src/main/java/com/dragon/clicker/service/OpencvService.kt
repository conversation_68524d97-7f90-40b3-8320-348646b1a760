package com.dragon.clicker.service

import android.annotation.SuppressLint
import android.app.*
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.HandlerThread
import android.os.IBinder
import android.provider.MediaStore
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.dragon.clicker.R
import com.dragon.clicker.config.ApiConstant
import com.dragon.clicker.config.GlobalVariabel
import com.dragon.clicker.config.Point
import com.dragon.clicker.config.TemplateConfig
import com.dragon.clicker.config.TemplateMatchResult
import com.dragon.clicker.monitoring.Monitoring
import kotlinx.coroutines.*
import org.opencv.android.Utils
import org.opencv.core.Core
import org.opencv.core.CvType
import org.opencv.core.Mat
import org.opencv.imgproc.Imgproc
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean
import android.hardware.display.VirtualDisplay
import androidx.core.graphics.createBitmap
import org.opencv.core.Rect
import kotlinx.coroutines.suspendCancellableCoroutine
import org.opencv.core.Scalar
import org.opencv.core.Size
import kotlin.math.abs
import kotlin.random.Random

class OpencvService : Service() {
    private var mediaProjection: MediaProjection? = null
    private val runningJobs = CopyOnWriteArrayList<Job>()
    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // Global resource pool for 3 concurrent template comparisons
    companion object {
        private const val MAX_CONCURRENT_COMPARISONS = 3
        private const val TEMPLATE_SIZE = 200

        // Template cache (moved from OpencvObject)
        private val templateCache = mutableMapOf<String, Mat>()

        private var globalImageReader: ImageReader? = null
        private var globalVirtualDisplay: VirtualDisplay? = null
        private var globalHandlerThread: HandlerThread? = null

        // Controlled capture mechanism to prevent callback spam
        private var isCapturing = AtomicBoolean(false)
        private var captureCompletionCallback: ((Boolean) -> Unit)? = null

        // VirtualDisplay state management for pause/resume
        private enum class VirtualDisplayState { PAUSED, ACTIVE, ERROR }
        private var virtualDisplayState = VirtualDisplayState.PAUSED

        // Cooldown management for VirtualDisplay frequency control
        private var lastCaptureTime = 0L
        private const val MIN_CAPTURE_INTERVAL = 200L // 200ms minimum between captures
        private var captureSkipCount = 0
        private var totalCaptureRequests = 0

        // Global variables for template loading (reused for all template operations)
        private var globalTemplateBitmap: Bitmap? = null
        private var globalTemplateMat: Mat? = null
        private var globalScaledTemplateMat: Mat? = null

        // Global variables for screen capture and processing
        private var globalScreenCaptureMat: Mat? = null
        private var globalSourceMat: Mat? = null

        // Global variables for image saving
        private var globalSaveImageMat: Mat? = null
        private var globalSaveImageBitmap: Bitmap? = null

        // Global variables for bitmap creation and conversion
        private var globalImageConversionBitmap: Bitmap? = null
        private var globalTempConversionBitmap: Bitmap? = null
        private var globalTempBitmap: Bitmap? = null // Add missing globalTempBitmap from working version
        private var globalSourceBitmap: Bitmap? = null

        // Global Mat variables (70 total = 7 types × 10 instances)
        private var globalScreenMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalRoiMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalScaledRoiMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalResultMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalScreenGrayMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalTemplateGrayMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalTemplateCloneMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalTemplateMaskMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }
        private var globalUsedMats = Array<Mat?>(MAX_CONCURRENT_COMPARISONS) { null }

        // Resource allocation tracking
        private val resourceInUse = BooleanArray(MAX_CONCURRENT_COMPARISONS) { false }
        private val resourceLock = Any()

        // Initialize global resources
        fun initializeGlobalResources() {
            synchronized(resourceLock) {
                // Initialize template loading resources
                if (globalTemplateMat == null) {
                    globalTemplateMat = Mat()
                    globalScaledTemplateMat = Mat()
                }

                // Initialize screen capture resources
                if (globalScreenCaptureMat == null) {
                    globalScreenCaptureMat = Mat()
                    globalSourceMat = Mat()
                }

                // Initialize image saving resources
                if (globalSaveImageMat == null) {
                    globalSaveImageMat = Mat()
                }

                repeat(MAX_CONCURRENT_COMPARISONS) { index ->
                    if (globalScreenMats[index] == null) {
                        globalScreenMats[index] = Mat()
                    }
                    if (globalRoiMats[index] == null) {
                        globalRoiMats[index] = Mat()
                    }
                    if (globalScaledRoiMats[index] == null) {
                        globalScaledRoiMats[index] = Mat()
                    }
                    if (globalResultMats[index] == null) {
                        globalResultMats[index] = Mat()
                    }
                    if (globalScreenGrayMats[index] == null) {
                        globalScreenGrayMats[index] = Mat()
                    }
                    if (globalTemplateGrayMats[index] == null) {
                        globalTemplateGrayMats[index] = Mat()
                    }
                    if (globalTemplateCloneMats[index] == null) {
                        globalTemplateCloneMats[index] = Mat()
                    }
                    if (globalTemplateMaskMats[index] == null) {
                        globalTemplateMaskMats[index] = Mat()
                    }
                    if (globalUsedMats[index] == null) {
                        globalUsedMats[index] = Mat()
                    }

                }
                Log.d(
                    ApiConstant.LOG_TAG,
                    "Global resources initialized: $MAX_CONCURRENT_COMPARISONS slots + template pool of $TEMPLATE_SIZE",
                )
            }
        }

        // Acquire a resource slot
        fun acquireResourceSlot(): Int? {
            synchronized(resourceLock) {
                val timestamp = System.currentTimeMillis()
                val activeSlotsBefore = resourceInUse.count { it }
                val callerInfo =
                    Thread.currentThread().stackTrace.getOrNull(3)?.let {
                        "${it.className}.${it.methodName}:${it.lineNumber}"
                    } ?: "Unknown"

//                Log.d(ApiConstant.LOG_TAG, "🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at $timestamp")
//                Log.d(ApiConstant.LOG_TAG, "🔍 GLOBAL MAT ACQUIRE: Called from: $callerInfo")
//                Log.d(ApiConstant.LOG_TAG, "🔍 GLOBAL MAT ACQUIRE: Active slots before: $activeSlotsBefore/$MAX_CONCURRENT_COMPARISONS")

                for (i in resourceInUse.indices) {
                    if (!resourceInUse[i]) {
                        resourceInUse[i] = true
                        val activeSlotAfter = resourceInUse.count { it }
//                        Log.d(ApiConstant.LOG_TAG, "✅ GLOBAL MAT ACQUIRE: Resource slot $i acquired successfully at $timestamp")
//                        Log.d(ApiConstant.LOG_TAG, "✅ GLOBAL MAT ACQUIRE: Active slots after: $activeSlotAfter/$MAX_CONCURRENT_COMPARISONS")
//                        Log.d(
//                            ApiConstant.LOG_TAG,
//                            "🔧 GLOBAL MAT ACQUIRE: Slot $i - Mat objects available: screen=${globalScreenMats[i] != null}, roi=${globalRoiMats[i] != null}, result=${globalResultMats[i] != null}",
//                        )
//                        Log.d(
//                            ApiConstant.LOG_TAG,
//                            "🔧 GLOBAL MAT ACQUIRE: Slot $i - Mat native addresses: screen=${globalScreenMats[i]?.nativeObj ?: 0}, roi=${globalRoiMats[i]?.nativeObj ?: 0}",
//                        )
                        return i
                    }
                }
//                Log.w(
//                    ApiConstant.LOG_TAG,
//                    "❌ GLOBAL MAT ACQUIRE: No available resource slots! All $MAX_CONCURRENT_COMPARISONS slots in use at $timestamp",
//                )
//                Log.w(
//                    ApiConstant.LOG_TAG,
//                    "🚨 GLOBAL MAT ACQUIRE: Resource exhaustion - consider increasing pool size or checking for leaks",
//                )
//                Log.w(ApiConstant.LOG_TAG, "🚨 GLOBAL MAT ACQUIRE: Called from: $callerInfo")

                // Force cleanup to try to free stuck resources
//                Log.w(ApiConstant.LOG_TAG, "🔧 GLOBAL MAT ACQUIRE: Attempting emergency cleanup")
                performPeriodicCleanup()

                // Try one more time after cleanup
                for (i in resourceInUse.indices) {
                    if (!resourceInUse[i]) {
                        resourceInUse[i] = true
                        Log.w(ApiConstant.LOG_TAG, "✅ GLOBAL MAT ACQUIRE: Emergency cleanup freed slot $i")
                        return i
                    }
                }

                return null
            }
        }

        // Release a resource slot
        fun releaseResourceSlot(slotIndex: Int) {
            synchronized(resourceLock) {
                val timestamp = System.currentTimeMillis()
                val callerInfo =
                    Thread.currentThread().stackTrace.getOrNull(3)?.let {
                        "${it.className}.${it.methodName}:${it.lineNumber}"
                    } ?: "Unknown"

//                Log.d(ApiConstant.LOG_TAG, "🔍 GLOBAL MAT RELEASE: Attempting to release slot $slotIndex at $timestamp")
//                Log.d(ApiConstant.LOG_TAG, "🔍 GLOBAL MAT RELEASE: Called from: $callerInfo")

                if (slotIndex in resourceInUse.indices) {
                    val activeSlotsBefore = resourceInUse.count { it }
                    val wasInUse = resourceInUse[slotIndex]
                    resourceInUse[slotIndex] = false
                    val activeSlotsAfter = resourceInUse.count { it }

                    if (wasInUse) {
//                        Log.d(ApiConstant.LOG_TAG, "✅ GLOBAL MAT RELEASE: Resource slot $slotIndex released successfully at $timestamp")
//                        Log.d(ApiConstant.LOG_TAG, "✅ GLOBAL MAT RELEASE: Active slots: $activeSlotsBefore -> $activeSlotsAfter")
//                        Log.d(
//                            ApiConstant.LOG_TAG,
//                            "🔧 GLOBAL MAT RELEASE: Slot $slotIndex - Mat objects status: screen=${globalScreenMats[slotIndex]?.empty() == false}, roi=${globalRoiMats[slotIndex]?.empty() == false}",
//                        )
//                        Log.d(
//                            ApiConstant.LOG_TAG,
//                            "🔧 GLOBAL MAT RELEASE: Slot $slotIndex - Mat native addresses: screen=${globalScreenMats[slotIndex]?.nativeObj ?: 0}, roi=${globalRoiMats[slotIndex]?.nativeObj ?: 0}",
//                        )
                    } else {
//                        Log.w(
//                            ApiConstant.LOG_TAG,
//                            "⚠️ GLOBAL MAT RELEASE: Attempted to release slot $slotIndex that was already free at $timestamp!",
//                        )
//                        Log.w(ApiConstant.LOG_TAG, "⚠️ GLOBAL MAT RELEASE: Called from: $callerInfo")
                    }
                } else {
                    Log.e(
                        ApiConstant.LOG_TAG,
                        "❌ GLOBAL MAT RELEASE: Invalid slot index $slotIndex for release (valid range: 0-${resourceInUse.size - 1}) at $timestamp",
                    )
                    Log.e(ApiConstant.LOG_TAG, "❌ GLOBAL MAT RELEASE: Called from: $callerInfo")
                }
            }
        }

        // Cleanup all global resources using safe release functions
        fun cleanupGlobalResources() {
            synchronized(resourceLock) {
                safeReleaseAllResources()
                cleanupGlobalScreenCaptureResources()
            }
        }

        // Enhanced cleanup for global screen capture resources
        private fun cleanupGlobalScreenCaptureResources() {
            Log.d(ApiConstant.LOG_TAG, "Starting enhanced screen capture resource cleanup...")

            // Enhanced ImageReader cleanup
            try {
                globalImageReader?.let { reader ->
                    // Clear listener first to prevent callbacks during cleanup
                    reader.setOnImageAvailableListener(null, null)
                    Log.d(ApiConstant.LOG_TAG, "ImageReader listener cleared")

                    // Drain any remaining images before closing
                    try {
                        var drainedCount = 0
                        var image = reader.acquireLatestImage()
                        while (image != null && drainedCount < 10) { // Limit to prevent infinite loop
                            image.close()
                            drainedCount++
                            image = reader.acquireLatestImage()
                        }
                        if (drainedCount > 0) {
                            Log.d(ApiConstant.LOG_TAG, "Drained $drainedCount images from ImageReader")
                        }
                    } catch (e: Exception) {
                        Log.w(ApiConstant.LOG_TAG, "Error draining ImageReader: ${e.message}")
                    }

                    reader.close()
                    Log.d(ApiConstant.LOG_TAG, "Enhanced ImageReader cleanup completed")
                }
                globalImageReader = null
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error in enhanced ImageReader cleanup", e)
            }

            // Enhanced VirtualDisplay cleanup
            try {
                globalVirtualDisplay?.let { display ->
                    // Get surface before releasing for cleanup
                    val surface = display.surface

                    display.release()
                    Log.d(ApiConstant.LOG_TAG, "VirtualDisplay released")

                    // Clean up surface if accessible
                    try {
                        surface?.release()
                        Log.d(ApiConstant.LOG_TAG, "VirtualDisplay surface released")
                    } catch (e: Exception) {
                        Log.w(ApiConstant.LOG_TAG, "Error releasing VirtualDisplay surface: ${e.message}")
                    }

                    Log.d(ApiConstant.LOG_TAG, "Enhanced VirtualDisplay cleanup completed")
                }
                globalVirtualDisplay = null
                virtualDisplayState = VirtualDisplayState.PAUSED
            } catch (e: Exception) {
                virtualDisplayState = VirtualDisplayState.ERROR
                Log.e(ApiConstant.LOG_TAG, "Error in enhanced VirtualDisplay cleanup", e)
            }

            // Enhanced HandlerThread cleanup
            try {
                globalHandlerThread?.let { thread ->
                    thread.quitSafely()
                    try {
                        thread.join(2000) // Increased timeout
                        Log.d(ApiConstant.LOG_TAG, "HandlerThread terminated gracefully")
                    } catch (e: InterruptedException) {
                        Log.w(ApiConstant.LOG_TAG, "HandlerThread did not terminate within timeout")
                        thread.interrupt() // Force interrupt if needed
                    }
                }
                globalHandlerThread = null
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error in enhanced HandlerThread cleanup", e)
            }

            // Force graphics memory cleanup
            try {
                System.gc()
                System.runFinalization()
                Log.d(ApiConstant.LOG_TAG, "Graphics memory cleanup completed")
            } catch (e: Exception) {
                Log.w(ApiConstant.LOG_TAG, "Error in graphics memory cleanup: ${e.message}")
            }
        }

        // Periodic cleanup to prevent resource accumulation
        fun performPeriodicCleanup() {
            Log.d(ApiConstant.LOG_TAG, "Performing periodic resource cleanup")

            // Force garbage collection to help free native memory
            try {
                System.gc()
                System.runFinalization()
            }catch(e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error in cleanGC", e)
            }

            synchronized(resourceLock) {
                val activeSlots = resourceInUse.count { it }
                if (activeSlots > 0) {
                    Log.w(ApiConstant.LOG_TAG, "Found $activeSlots active resource slots during periodic cleanup")
                }
            }
        }

        // === Safe Resource Release Functions ===
        private fun safeReleaseMat(
            mat: Mat?,
            name: String = "Mat",
        ) {
            try {
                mat?.release()
            } catch (e: Exception) {
                Log.w(ApiConstant.LOG_TAG, "Failed to release $name: ${e.message}")
            }
        }

        private fun safeRecycleBitmap(
            bitmap: Bitmap?,
            name: String = "Bitmap",
        ) {
            try {
                bitmap?.recycle()
            } catch (e: Exception) {
                Log.w(ApiConstant.LOG_TAG, "Failed to recycle $name: ${e.message}")
            }
        }

        // Safe release multiple Mats - continues even if some fail
        private fun safeReleaseMatArray(
            mats: Array<Mat?>,
            baseName: String,
        ) {
            for (i in mats.indices) {
                safeReleaseMat(mats[i], "$baseName[$i]")
                mats[i] = null
            }
        }

        // Safe bitmap operations to prevent recycled bitmap access
        private fun safeBitmapOperation(bitmap: Bitmap?, operation: String, action: (Bitmap) -> Unit): Boolean {
            return try {
                if (bitmap != null && !bitmap.isRecycled) {
                    action(bitmap)
                    true
                } else {
                    Log.w(ApiConstant.LOG_TAG, "⚠️ Attempted $operation on recycled or null bitmap")
                    false
                }
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "❌ Error in $operation: ${e.message}")
                false
            }
        }

        private fun safeBitmapDimensions(bitmap: Bitmap?): Pair<Int, Int>? {
            return if (bitmap != null && !bitmap.isRecycled) {
                try {
                    Pair(bitmap.width, bitmap.height)
                } catch (e: Exception) {
                    Log.w(ApiConstant.LOG_TAG, "⚠️ Error getting bitmap dimensions: ${e.message}")
                    null
                }
            } else {
                Log.w(ApiConstant.LOG_TAG, "⚠️ Cannot get dimensions of recycled or null bitmap")
                null
            }
        }

        // Safe release multiple resources with detailed logging
        private fun safeReleaseAllResources() {
            Log.d(ApiConstant.LOG_TAG, "Starting safe resource cleanup...")

            // Release all bitmaps with safe checks
            safeRecycleBitmap(globalSourceBitmap, "globalSourceBitmap")
            globalSourceBitmap = null
            safeRecycleBitmap(globalTemplateBitmap, "globalTemplateBitmap")
            globalTemplateBitmap = null
            safeRecycleBitmap(globalSaveImageBitmap, "globalSaveImageBitmap")
            globalSaveImageBitmap = null
            safeRecycleBitmap(globalImageConversionBitmap, "globalImageConversionBitmap")
            globalImageConversionBitmap = null
            safeRecycleBitmap(globalTempConversionBitmap, "globalTempConversionBitmap")
            globalTempConversionBitmap = null
            safeRecycleBitmap(globalTempBitmap, "globalTempBitmap")
            globalTempBitmap = null

            // Release all utility Mats
            safeReleaseMat(globalTemplateMat, "globalTemplateMat")
            globalTemplateMat = null
            safeReleaseMat(globalScaledTemplateMat, "globalScaledTemplateMat")
            globalScaledTemplateMat = null

            safeReleaseMat(globalScreenCaptureMat, "globalScreenCaptureMat")
            globalScreenCaptureMat = null
            safeReleaseMat(globalSourceMat, "globalSourceMat")
            globalSourceMat = null
            safeReleaseMat(globalSaveImageMat, "globalSaveImageMat")
            globalSaveImageMat = null

            // Release template cache - return Mats to pool or release them
            templateCache.forEach { (path, mat) ->
                try {
                    Log.d(ApiConstant.LOG_TAG, "Releasing template Mat from cache: $path")
                    safeReleaseMat(mat)
                } catch (e: Exception) {
                    Log.e(ApiConstant.LOG_TAG, "Failed to release template Mat from cache: ${e.message}")
                }
            }
            templateCache.clear()

            // Release comparison resource arrays
            safeReleaseMatArray(globalScreenMats, "globalScreenMats")
            safeReleaseMatArray(globalRoiMats, "globalRoiMats")
            safeReleaseMatArray(globalScaledRoiMats, "globalScaledRoiMats")
            safeReleaseMatArray(globalResultMats, "globalResultMats")
            safeReleaseMatArray(globalScreenGrayMats, "globalScreenGrayMats")
            safeReleaseMatArray(globalTemplateGrayMats, "globalTemplateGrayMats")
            safeReleaseMatArray(globalTemplateCloneMats, "globalTemplateCloneMats")
            safeReleaseMatArray(globalTemplateMaskMats, "globalTemplateMaskMats")
            safeReleaseMatArray(globalUsedMats, "globalUsedMats")

            // Reset resource tracking
            for (i in resourceInUse.indices) {
                resourceInUse[i] = false
            }

            Log.d(ApiConstant.LOG_TAG, "Safe resource cleanup completed")
        }

        // === Template Loading Functions (using global variables) ===
        private fun loadTemplateInternal(
            context: Context,
            assetPath: String,
        ): Mat {
            synchronized(resourceLock) {
                try {
                    context.assets.open(assetPath).use { inputStream ->
                        val options =
                            BitmapFactory.Options().apply {
                                inPreferredConfig = Bitmap.Config.ARGB_8888
                                inScaled = false
                            }

                        // Reuse global bitmap instead of creating new one
                        safeRecycleBitmap(globalTemplateBitmap, "globalTemplateBitmap") // Clean up previous bitmap if exists
                        globalTemplateBitmap = BitmapFactory.decodeStream(inputStream, null, options)
                            ?: throw IllegalStateException("Failed to decode bitmap: $assetPath")

//                        // ✅ RESIZE TEMPLATE CHO RESOLUTION MỚI TRƯỚC KHI TÍNH SCALE
//                        if (GlobalVariabel.screenWidthReal != 2560) {
//                            val resolutionScale = GlobalVariabel.screenWidthReal.toDouble() / 2560.0
//                            val newWidth = (globalTemplateBitmap!!.width * resolutionScale).toInt()
//                            val newHeight = (globalTemplateBitmap!!.height * resolutionScale).toInt()
//
//                            val resizedBitmap = globalTemplateBitmap!!.scale(newWidth, newHeight)
//
//                            globalTemplateBitmap!!.recycle()
//                            globalTemplateBitmap = resizedBitmap
//
//                            Log.d(ApiConstant.LOG_TAG,
//                                "Template pre-resized for ${GlobalVariabel.screenWidthReal}x${GlobalVariabel.screenHeightReal}: " +
//                                        "$assetPath (${globalTemplateBitmap!!.width}x${globalTemplateBitmap!!.height})"
//                            )
//                        }

                        // Reuse global Mat instead of creating new one with safe bitmap access
                        val success = safeBitmapOperation(globalTemplateBitmap, "template bitmapToMat conversion") { bitmap ->
                            Utils.bitmapToMat(bitmap, globalTemplateMat!!)
                        }
                        if (!success) {
                            throw IllegalStateException("Failed to convert template bitmap to Mat: $assetPath")
                        }

                        Log.d(ApiConstant.LOG_TAG, "Template loaded using global resources: $assetPath, size=${globalTemplateMat!!.size()}")
                        return globalTemplateMat!!
                    }
                } catch (e: Exception) {
                    Log.e(ApiConstant.LOG_TAG, "Error loading template: $assetPath", e)
                    throw e
                }
            }
        }

        fun loadTemplate(
            context: Context,
            templateConfig: TemplateConfig,
        ): Mat {
            synchronized(resourceLock) {
                if (templateCache.size > TEMPLATE_SIZE) {
                    templateCache.forEach { (path, mat) ->
                        try {
                            Log.d(ApiConstant.LOG_TAG, "Releasing template Mat from cache: $path")
                            mat.release()
                        } catch (e: Exception) {
                            Log.e(ApiConstant.LOG_TAG, "Failed to release template Mat from cache: ${e.message}")
                        }
                    }
                    templateCache.clear()
                }

                val assetPath = templateConfig.path
                val scale = templateConfig.scale

                // Check if we already have this scaled template in cache
                templateCache[assetPath]?.let { cachedTemplate ->
                    if (!cachedTemplate.empty()) {
                        Log.d(ApiConstant.LOG_TAG, "Cache hit: $assetPath (scale: $scale)")
                        return cachedTemplate
                    } else {
                        try {
                            Log.d(ApiConstant.LOG_TAG, "Removed empty template from cache: $assetPath")
                            templateCache[assetPath]?.release()
                        } catch (e: Exception) {
                            Log.e(ApiConstant.LOG_TAG, "Failed to release template Mat from cache: ${e.message}")
                        } finally {
                            templateCache.remove(assetPath)
                        }
                    }
                }

                var originalMat: Mat? = null
                var scaledMat: Mat? = null
                try {
                    // Load the original template using global resources
                    Log.d(ApiConstant.LOG_TAG, "Cache miss, loading: $assetPath (scale: $scale)")
                    originalMat = loadTemplateInternal(context, assetPath)

                    // Apply scaling if needed using global scaled template Mat
                    scaledMat =
                        if (scale != 1.0) {
                            val newSize =
                                Size(
                                    originalMat.width() * scale,
                                    originalMat.height() * scale,
                                )
                            // Use global scaled template Mat instead of creating new one
                            Imgproc.resize(originalMat, globalScaledTemplateMat!!, newSize, 0.0, 0.0, Imgproc.INTER_AREA)
                            Log.d(
                                ApiConstant.LOG_TAG,
                                "Template scaled using global resources: $assetPath, " +
                                    "original size=${originalMat.size()}, scaled size=${globalScaledTemplateMat!!.size()}, scale=$scale",
                            )
                            // Use global cache Mat for scaled result instead of creating new Mat
                            globalScaledTemplateMat!!.clone()
                        } else {
                            Log.d(ApiConstant.LOG_TAG, "Template no scaling needed: $assetPath, size=${originalMat.size()}")
                            originalMat.clone()
                        }

                    // Cache the final template and return a clone
                    templateCache[assetPath] = scaledMat
                    val resultMat = scaledMat.clone()

                    // Don't release scaledMat here - it's now cached
                    return resultMat
                } catch (e: Exception) {
                    Log.e(ApiConstant.LOG_TAG, "Error loading template: $assetPath", e)
                    // Only release on error
                    safeReleaseMat(scaledMat)
                    throw e
                } finally {
                    // Always release originalMat as it's not cached
                    safeReleaseMat(originalMat)
                    originalMat = null
                    scaledMat = null // Clear reference but don't release (it's cached)
                }
            }
        }

        // Service instance and constants
        var instance: OpencvService? = null
        private const val NOTIFICATION_ID = 102
        private const val CHANNEL_ID = "opencv_service_channel"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(ApiConstant.LOG_TAG, "OpencvService onCreate() called")
        try {
            instance = this
            initializeGlobalResources()
            Log.d(ApiConstant.LOG_TAG, "OpencvService created with global resources initialized")
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Error in OpencvService onCreate", e)
            throw e
        }
//        try {
//            if (!isMLKitWarmedUp) {
//                isMLKitWarmedUp = true
//
//                // Tạo 1 ảnh trắng nhỏ để "warm up"
//                val preloadBitmap = createBitmap(10, 10)
//                val preloadImage = InputImage.fromBitmap(preloadBitmap, 0)
//
//                TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
//                    .process(preloadImage)
//                    .addOnFailureListener {
//                        Log.w(ApiConstant.LOG_TAG, "❗ ML Kit chưa tải xong hoặc chưa có mạng")
//                    }
//            }
//            Log.d(ApiConstant.LOG_TAG, "OpencvService created with global resources initialized")
//        } catch (e: Exception) {
//            Log.e(ApiConstant.LOG_TAG, "Error in OpencvService onCreate", e)
//            throw e
//        }
    }

    @SuppressLint("ForegroundServiceType")
    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        Log.d(ApiConstant.LOG_TAG, "OpencvService onStartCommand() called")
        try {
            val resultCode = intent?.getIntExtra("resultCode", -1) ?: -1
            val resultData = intent?.getParcelableExtra<Intent>("resultData")

            Log.d(ApiConstant.LOG_TAG, "OpencvService resultCode: $resultCode, resultData: $resultData")

            Log.d(ApiConstant.LOG_TAG, "OpencvService: Activity.RESULT_OK = ${Activity.RESULT_OK}")
            if (resultCode != Activity.RESULT_OK || resultData == null) {
                Log.e(ApiConstant.LOG_TAG, "OpencvService: Screen capture permission not granted - resultCode: $resultCode (expected: ${Activity.RESULT_OK}), resultData: $resultData")
                Toast.makeText(this, "Screen capture permission not granted", Toast.LENGTH_SHORT).show()
                stopSelf()
                return START_NOT_STICKY
            }

            Log.d(ApiConstant.LOG_TAG, "OpencvService: Creating MediaProjection")
            val projectionManager = getSystemService(MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            if (mediaProjection == null) {
                mediaProjection = projectionManager.getMediaProjection(resultCode, resultData)
                Log.d(ApiConstant.LOG_TAG, "OpencvService: MediaProjection created: $mediaProjection")
            } else {
                Log.d(ApiConstant.LOG_TAG, "OpencvService: MediaProjection already exists: $mediaProjection")
            }

            Log.d(ApiConstant.LOG_TAG, "OpencvService: Setting up notification and starting foreground")
            setupNotificationChannel()
            startForeground(NOTIFICATION_ID, createNotification())

            Log.d(ApiConstant.LOG_TAG, "OpencvService started successfully")
            return START_NOT_STICKY
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Error in OpencvService onStartCommand", e)
            stopSelf()
            return START_NOT_STICKY
        }
    }

    private fun setupNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelName = "Opencv Service Automation"
            val importance = NotificationManager.IMPORTANCE_LOW
            val notificationChannel = NotificationChannel(CHANNEL_ID, channelName, importance)
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(notificationChannel)
        }
    }

    private fun createNotification(): Notification =
        NotificationCompat
            .Builder(this, CHANNEL_ID)
            .setContentTitle("Opencv Service Running")
            .setContentText("The service is automating Opencv.")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setOngoing(true)
            .build()

    override fun onDestroy() {
        Log.d(ApiConstant.LOG_TAG, "OpencvService destroyed")
        instance = null
        coroutineScope.cancel()
        synchronized(runningJobs) {
            runningJobs.forEach { it.cancel() }
            runningJobs.clear()
        }

        // Stop media projection
        mediaProjection?.stop()
        mediaProjection = null

        // Cleanup global resources
        cleanupGlobalResources()
        checkForUnreleasedMats()
        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? = null

//    private fun initTesseract(context: Context) {
//        if (tess == null) {
//            copyTrainedDataIfNeeded(context)
//            tess = TessBaseAPI()
//            Log.w(ApiConstant.LOG_TAG,"Match: ENG")
//            val success = tess!!.init(context.filesDir.absolutePath, "eng")
//            if (!success) {
//                Log.e(ApiConstant.LOG_TAG, "❌ Tesseract init failed")
//                tess = null
//            }
//        }
//    }

//    private fun ocrBitmap(bitmap: Bitmap): String? {
//        val tesseract = tess ?: return null
//        tesseract.setVariable(TessBaseAPI.VAR_CHAR_WHITELIST, "0123456789")
//        tesseract.setImage(bitmap)
//        return tesseract.utF8Text
//    }

//    private fun copyTrainedDataIfNeeded(context: Context) {
//        val tessDataPath = File(context.filesDir, "tessdata")
//        val trainedDataFile = File(tessDataPath, "eng.traineddata")
//        if (!trainedDataFile.exists()) {
//            tessDataPath.mkdirs()
//            context.assets.open("tessdata/eng.traineddata").use { input ->
//                FileOutputStream(trainedDataFile).use { output ->
//                    input.copyTo(output)
//                }
//            }
//        }
//    }

    // === Screen Setup ===
    private suspend fun initScreen(): Boolean =
        withContext(Dispatchers.Main) {
            try {
                val wm = getSystemService(WINDOW_SERVICE) as WindowManager
                val metrics = DisplayMetrics()
                wm.defaultDisplay.getRealMetrics(metrics)
                GlobalVariabel.screenWidthReal = metrics.widthPixels
                GlobalVariabel.screenHeightReal = metrics.heightPixels
                Log.d(ApiConstant.LOG_TAG, "Screen initialized: ${metrics.widthPixels}x${metrics.heightPixels}")
                true
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Failed to initialize screen", e)
                false
            }
        }

    // === Screen Capture using global variables ===
    suspend fun saveScreen() {
        Log.d(ApiConstant.LOG_TAG, "saveScreen() called")
        if (!initScreen()) {
            Log.e(ApiConstant.LOG_TAG, "Failed to initialize screen")
            return
        }
        try {
            Log.d(ApiConstant.LOG_TAG, "saveScreen: About to call getSourceBitmap")
            val startTime = System.nanoTime()

            // Capture bitmap and store in global variable
            val success = getSourceBitmap(GlobalVariabel.screenWidthReal, GlobalVariabel.screenHeightReal, 560)
            if (!success) {
                Log.e(ApiConstant.LOG_TAG, "Failed to capture bitmap")
                return
            }

            // Then use synchronized block only for global resource operations with safe bitmap access
            synchronized(resourceLock) {
                val tmpSuccess = safeBitmapOperation(globalSourceBitmap, "bitmapToMat conversion") { bitmap ->
                    Utils.bitmapToMat(bitmap, globalScreenCaptureMat!!)
                }
                if (!tmpSuccess) {
                    Log.e(ApiConstant.LOG_TAG, "Failed to convert bitmap to Mat - bitmap was recycled")
                    return
                }
            }

            Log.d(ApiConstant.LOG_TAG, "grabScreenshot completed in ${(System.nanoTime() - startTime) / 1_000_000.0} ms")
            Log.d(ApiConstant.LOG_TAG, "Screen captured using global resources: ${globalScreenCaptureMat!!.size()}")

            saveMatAsImage(globalScreenCaptureMat!!, "Screen", ApiConstant.IMG_PATH)
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Error saving screenshot", e)
        }
    }

    private suspend fun compareListImagesSequential(
        templateConfigs: List<TemplateConfig>,
        threshold: Double,
        gray: Boolean,
        mask: Boolean
    ): Pair<String, TemplateMatchResult?> =
        withContext(Dispatchers.Default) {
            val startTime = System.nanoTime()

            try {
                // 🔧 MEMORY LEAK FIX: Add delay between screen captures to reduce VirtualDisplay create/destroy frequency
                delay(50) // Small delay to reduce memory pressure
                Log.d(ApiConstant.LOG_TAG, "🔄 Sequential processing ${templateConfigs.size} templates")

                // Capture screen once and store in global variable
                val success = getSourceBitmap(GlobalVariabel.screenWidthReal, GlobalVariabel.screenHeightReal, 560)
                if (!success) {
                    Log.e(ApiConstant.LOG_TAG, "Failed to capture bitmap for template comparison")
                    return@withContext Pair(ApiConstant.FAILED, null)
                }

                // 🔧 MEMORY LEAK FIX: Force garbage collection after screen capture to help with native memory
                // cleanGC() // Commented out for now

                // Then use synchronized block only for global resource operations with safe bitmap access
                synchronized(resourceLock) {
                    val tmpSuccess = safeBitmapOperation(globalSourceBitmap, "bitmapToMat conversion") { bitmap ->
                        Utils.bitmapToMat(bitmap, globalScreenCaptureMat!!)
                    }
                    if (!tmpSuccess) {
                        Log.e(ApiConstant.LOG_TAG, "Failed to convert bitmap to Mat - bitmap was recycled")
                        return@withContext Pair(ApiConstant.FAILED, null)
                    }
                }
                Log.d(ApiConstant.LOG_TAG, "grabScreenshot completed in ${(System.nanoTime() - startTime) / 1_000_000.0} ms")

                val monitoring = Monitoring()
                monitoring.logSystemMemory(applicationContext)

                Log.d(
                    ApiConstant.LOG_TAG,
                    "templatePaths.forEach started at ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms",
                )

                // Limit concurrent processing to available resource slots
                val limitedConfigs = templateConfigs.take(MAX_CONCURRENT_COMPARISONS)
                Log.d(ApiConstant.LOG_TAG, "Processing ${limitedConfigs.size} templates (limited to $MAX_CONCURRENT_COMPARISONS)")

                for (tmplConfig in limitedConfigs) {
                    Log.d(
                        ApiConstant.LOG_TAG,
                        "templatePaths.forEach ${tmplConfig.path} ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms",
                    )
                    val slotIndex = acquireResourceSlot()
                    if (slotIndex == null) {
                        Log.w(ApiConstant.LOG_TAG, "No available resource slot for template: ${tmplConfig.path}")
                        continue
                    }

                    try {
                        // Use global resources instead of creating new ones
                        val screenCopy = globalScreenMats[slotIndex]!!
                        globalScreenCaptureMat!!.copyTo(screenCopy)

                        Log.d(
                            ApiConstant.LOG_TAG,
                            "templatePaths.forEach matching ${tmplConfig.path} using slot $slotIndex ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms",
                        )

                        val match =
                            performTemplateMatchingWithGlobalResources(
                                tmplConfig,
                                screenCopy,
                                threshold,
                                gray,
                                slotIndex,
                                mask
                            )

                        Log.d(
                            ApiConstant.LOG_TAG,
                            "templatePaths.forEach matching end ${tmplConfig.path} slot $slotIndex ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms",
                        )

                        match?.let {
                            Log.d(ApiConstant.LOG_TAG, "MATCH FOUND: ${tmplConfig.path}")
                            return@withContext ApiConstant.DONE to TemplateMatchResult(tmplConfig.path, Point(it.x, it.y))
                        }

                        delay(50)
                    } catch (e: Exception) {
                        if (e is CancellationException) {
                            Log.d(ApiConstant.LOG_TAG, "Template matching cancelled: ${tmplConfig.path} slot $slotIndex")
                        } else {
                            Log.e(
                                ApiConstant.LOG_TAG,
                                "compareListImage error: ${tmplConfig.path} slot $slotIndex - ${e.localizedMessage}",
                            )
                        }
                    } finally {
                        releaseResourceSlot(slotIndex)
                    }
                }
                ApiConstant.FAILED to null
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Critical error in compareListImage: $e")
                ApiConstant.FAILED to null
            } finally {
                // CRITICAL: Always cleanup resources
                try {
                    checkForUnreleasedMats()
                    Log.d(ApiConstant.LOG_TAG, "compareListImage cleanup completed")
                } catch (e: Exception) {
                    Log.e(ApiConstant.LOG_TAG, "Error during compareListImage cleanup: $e")
                }
            }
        }

    // === Template matching using global resources ===
    private fun performTemplateMatchingWithGlobalResources(
        templateConfig: TemplateConfig,
        screen: Mat,
        threshold: Double,
        gray: Boolean,
        slotIndex: Int,
        mask: Boolean
    ): Point? {
        if (screen.empty()) {
            Log.e(ApiConstant.LOG_TAG, "Invalid image: screen=${screen.size()}, path=${templateConfig.path}")
            return null
        }

        // Get the cached template (already scaled) and copy to slot-specific Mat to prevent sharing
        val cachedTemplate = loadTemplate(this, templateConfig)
        val template = globalTemplateCloneMats[slotIndex]!!
        cachedTemplate.copyTo(template)

        // Release the clone immediately to prevent memory leak
        cachedTemplate.release()

        val result = globalResultMats[slotIndex]!!

        try {
            // Extract ROI if specified using global ROI mat
            val roiMat =
                if (templateConfig.rect != null) {
                    val roi = globalRoiMats[slotIndex]!!

                    // Create proper OpenCV Rect for submat extraction
                    // OpenCV Rect(x, y, width, height) but submat needs proper bounds checking
                    val rect = templateConfig.rect
                    val maxX = minOf(rect.x + rect.width, screen.width())
                    val maxY = minOf(rect.y + rect.height, screen.height())
                    val actualWidth = maxX - rect.x
                    val actualHeight = maxY - rect.y

                    // Create a new Rect with validated bounds
                    val validRect = Rect(rect.x, rect.y, actualWidth, actualHeight)

                    screen.submat(validRect).copyTo(roi)

                    // Log original ROI dimensions
                    Log.d(
                        ApiConstant.LOG_TAG,
                        "Original ROI extracted slot $slotIndex: ${templateConfig.path} - " +
                            "original rect=${templateConfig.rect}, valid rect=$validRect, " +
                            "roiMat size=${roi.size()} (${roi.width()}x${roi.height()})",
                    )

                    roi
                } else {
                    screen
                }
            // Scale the ROI to match the template scale using global scaled ROI mat
            val scale = templateConfig.scale
            val scaledRoiMat = globalScaledRoiMats[slotIndex]!!
            if (scale != 1.0) {
                // Calculate new size based on scale
                val newSize =
                    Size(
                        roiMat.width() * scale,
                        roiMat.height() * scale,
                    )

                // Log scaling calculation
                Log.d(
                    ApiConstant.LOG_TAG,
                    "ROI scaling slot $slotIndex: ${templateConfig.path} - " +
                        "original size=${roiMat.size()} (${roiMat.width()}x${roiMat.height()}), " +
                        "scale=$scale, newSize=$newSize (${newSize.width.toInt()}x${newSize.height.toInt()})",
                )

                // Resize the ROI
                Imgproc.resize(roiMat, scaledRoiMat, newSize, 0.0, 0.0, Imgproc.INTER_AREA)
            } else {
                // No scaling needed, just copy the ROI
                roiMat.copyTo(scaledRoiMat)

                Log.d(
                    ApiConstant.LOG_TAG,
                    "ROI no scaling slot $slotIndex: ${templateConfig.path} - " +
                        "size=${roiMat.size()} (${roiMat.width()}x${roiMat.height()})",
                )
            }

            // Log dimensions before matchTemplate to debug size mismatch
            Log.d(
                ApiConstant.LOG_TAG,
                "Template matching dimensions slot $slotIndex: ${templateConfig.path} - " +
                    "scaledRoiMat size=${scaledRoiMat.size()} (${scaledRoiMat.width()}x${scaledRoiMat.height()}), " +
                    "template size=${template.size()} (${template.width()}x${template.height()}), " +
                    "scale=${templateConfig.scale}, gray=$gray",
            )

            // Validate dimensions before matchTemplate
            if (scaledRoiMat.width() < template.width() || scaledRoiMat.height() < template.height()) {
                Log.e(
                    ApiConstant.LOG_TAG,
                    "Template matching error slot $slotIndex: ${templateConfig.path} - " +
                        "Template (${template.width()}x${template.height()}) is larger than " +
                        "scaled ROI (${scaledRoiMat.width()}x${scaledRoiMat.height()}). " +
                        "Cannot perform template matching.",
                )
                return null
            }

            if (gray) {
                val screenGray = globalScreenGrayMats[slotIndex]!!
                val templateGray = globalTemplateGrayMats[slotIndex]!!
                Imgproc.cvtColor(scaledRoiMat, screenGray, Imgproc.COLOR_BGR2GRAY)
                Imgproc.cvtColor(template, templateGray, Imgproc.COLOR_BGR2GRAY)

                // Log gray dimensions as well
                Log.d(
                    ApiConstant.LOG_TAG,
                    "Gray template matching dimensions slot $slotIndex: ${templateConfig.path} - " +
                        "screenGray size=${screenGray.size()} (${screenGray.width()}x${screenGray.height()}), " +
                        "templateGray size=${templateGray.size()} (${templateGray.width()}x${templateGray.height()})",
                )

                // Validate gray dimensions as well
                if (screenGray.width() < templateGray.width() || screenGray.height() < templateGray.height()) {
                    Log.e(
                        ApiConstant.LOG_TAG,
                        "Gray template matching error slot $slotIndex: ${templateConfig.path} - " +
                            "Template gray (${templateGray.width()}x${templateGray.height()}) is larger than " +
                            "screen gray (${screenGray.width()}x${screenGray.height()}). " +
                            "Cannot perform template matching.",
                    )
                    return null
                }
                if(mask){
                    var templateConfigMask = templateConfig
                    when(templateConfig.path){
                        ApiConstant.SMG935F_SICKLE_TEMPLATE.path -> templateConfigMask = ApiConstant.SMG935F_SICKLE_MASK
                        ApiConstant.SMG935F_WHEAT_ICON_ZERO.path -> templateConfigMask = ApiConstant.SMG935F_WHEAT_ICON_ZERO_MASK
                        ApiConstant.SMG935F_ADS_WHEAT_ICON.path -> templateConfigMask = ApiConstant.SMG935F_ADS_WHEAT_ICON_MASK
                    }
                    val cachedTemplateMask = loadTemplate(this, templateConfigMask)
                    val maskMat = globalTemplateMaskMats[slotIndex]!!
                    cachedTemplateMask.copyTo(maskMat)
                    // Release the clone immediately to prevent memory leak
                    cachedTemplateMask.release()
                    Imgproc.matchTemplate(screenGray, templateGray, result, Imgproc.TM_CCORR_NORMED, maskMat)
                }else{
                    Imgproc.matchTemplate(screenGray, templateGray, result, Imgproc.TM_CCOEFF_NORMED)
                }
            } else {
                if(mask){
                    var templateConfigMask = templateConfig
                    when(templateConfig.path){
                        ApiConstant.SMG935F_SICKLE_TEMPLATE.path -> templateConfigMask = ApiConstant.SMG935F_SICKLE_MASK
                        ApiConstant.SMG935F_WHEAT_ICON_ZERO.path -> templateConfigMask = ApiConstant.SMG935F_WHEAT_ICON_ZERO_MASK
                        ApiConstant.SMG935F_ADS_WHEAT_ICON.path -> templateConfigMask = ApiConstant.SMG935F_ADS_WHEAT_ICON_MASK
                    }
                    val cachedTemplateMask = loadTemplate(this, templateConfigMask)
                    val maskMat = globalTemplateMaskMats[slotIndex]!!
                    cachedTemplateMask.copyTo(maskMat)
                    // Release the clone immediately to prevent memory leak
                    cachedTemplateMask.release()
                    Imgproc.matchTemplate(scaledRoiMat, template, result, Imgproc.TM_CCOEFF_NORMED,maskMat)
                }else{
                    Imgproc.matchTemplate(scaledRoiMat, template, result, Imgproc.TM_CCOEFF_NORMED)
                }
            }

            // Get match results
            val mmr = Core.minMaxLoc(result)

            // Calculate center point of the match
            val matchX = (templateConfig.rect.x.toDouble())
                .plus((mmr.maxLoc.x + template.cols() / 2.0) / scale)
            val matchY = (templateConfig.rect.y.toDouble())
                .plus((mmr.maxLoc.y + template.rows() / 2.0) / scale)

            Log.d(
                ApiConstant.LOG_TAG,
                "Template match slot $slotIndex: path=${templateConfig.path}, maxVal=${mmr.maxVal}, threshold=$threshold, point=($matchX, $matchY)",
            )

            // 🔧 TESTING: Add success detection logs
            if (templateConfig.path == ApiConstant.SMG935F_HOME_GRAY.path) {
                val screenBrightness = calculateAverageBrightness(scaledRoiMat, slotIndex)
                val isMatch = mmr.maxVal >= threshold && screenBrightness < 100
                if (isMatch) {
                    Log.d(ApiConstant.LOG_TAG, "🎉 SUCCESS: Template match FOUND! ${templateConfig.path} with maxVal=${mmr.maxVal} >= threshold=$threshold AND brightness=$screenBrightness < 100")
                } else {
                    Log.d(ApiConstant.LOG_TAG, "❌ FAIL: Template match failed. ${templateConfig.path} maxVal=${mmr.maxVal} >= threshold=$threshold? ${mmr.maxVal >= threshold}, brightness=$screenBrightness < 100? ${screenBrightness < 100}")
                }
                return if (isMatch) Point(matchX.toInt(), matchY.toInt()) else null
            } else {
                val isMatch = mmr.maxVal >= threshold
                if (isMatch) {
                    Log.d(ApiConstant.LOG_TAG, "🎉 SUCCESS: Template match FOUND! ${templateConfig.path} with maxVal=${mmr.maxVal} >= threshold=$threshold")
                } else {
                    Log.d(ApiConstant.LOG_TAG, "❌ FAIL: Template match below threshold. ${templateConfig.path} maxVal=${mmr.maxVal} < threshold=$threshold")
                }
                return if (isMatch) Point(matchX.toInt(), matchY.toInt()) else null
            }
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Template matching error slot $slotIndex: ${templateConfig.path}", e)
            return null
        }
        // No need to release template - it's from cache and managed globally
    }

    // Calculate brightness using global resources
    private fun calculateAverageBrightness(
        image: Mat,
        slotIndex: Int,
    ): Double {
        val grayMat = globalTemplateGrayMats[slotIndex]!! // Reuse gray mat
        Imgproc.cvtColor(image, grayMat, Imgproc.COLOR_BGR2GRAY)
        val mean = Core.mean(grayMat)
        return mean.`val`[0]
    }

    // === Repeat compare until success or timeout ===
    suspend fun compareImagesLong(
        templateConfigs: List<TemplateConfig>,
        timeoutMillis: Long,
        threshold: Double,
        gray: Boolean,
        mask: Boolean = false,
        reconnect: Boolean = false
    ): Pair<String, TemplateMatchResult?> =
        withContext(Dispatchers.Default) {
            val startTime = System.currentTimeMillis()
            val endTime = startTime + timeoutMillis
            val isMatched = AtomicBoolean(false)
            val monitoring = Monitoring()
            var finalResult: Pair<String, TemplateMatchResult?> = ApiConstant.FAILED to null
            var iteration = 0
            try {
                while (System.currentTimeMillis() < endTime && !isMatched.get()){
                    val currentTime = System.currentTimeMillis()
                    val elapsedTime = currentTime - startTime

                    Log.d(ApiConstant.LOG_TAG,
                        "compareListImage started iteration $iteration at ${elapsedTime}ms " +
                                "(remaining: ${endTime - currentTime}ms)")

                    monitoring.logSystemMemory(applicationContext)
//                    Log.d(ApiConstant.LOG_TAG, "=== SYSTEM HEALTH CHECK BEFORE PROCESSING ===")
                    // val cpuOK = checkCpuLoad() // Commented out for now
                    // val memoryOK = checkMemoryPressure() // Commented out for now
                    // val thermalOK = checkThermalState() // Commented out for now
                    // val graphicsOK = checkGraphicsHealth() // Commented out for now

                    // Log.d(ApiConstant.LOG_TAG, "System Health: CPU=$cpuOK, Memory=$memoryOK, Thermal=$thermalOK, Graphics=$graphicsOK")
                    try {
                        val result = compareListImagesSequential(templateConfigs, threshold, gray, mask)
                        if (result.first == ApiConstant.DONE && isMatched.compareAndSet(false, true)) {
                            Log.d(ApiConstant.LOG_TAG,
                                "Match found at iteration $iteration after ${elapsedTime}ms: ${result.second?.templatePath}")
                            finalResult = result
                            break // Exit the while loop
                        }else{
                            if(reconnect){
                                MyAccessibilityServiceRoot.instance?.performClickAt(listOf(ApiConstant.POINT_RECONECT.withRandomOffset(ApiConstant.OFFSET_SMALL)), onComplete = {})
                                Log.d(ApiConstant.LOG_TAG,"Reconnect Clicked")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(ApiConstant.LOG_TAG, "Image comparison failed at iteration $iteration: $e")
                    }

                    iteration++

                    // Ensure minimum delay between iterations to prevent CPU overload
                    val iterationTime = System.currentTimeMillis() - currentTime
                    val minIterationTime = 100L // Minimum 100ms per iteration
                    if (iterationTime < minIterationTime) {
                        delay(minIterationTime - iterationTime)
                    }
                }
                val totalTime = System.currentTimeMillis() - startTime
                Log.d(ApiConstant.LOG_TAG,
                    "compareImagesLong completed: $iteration iterations in ${totalTime}ms " +
                            "(timeout was ${timeoutMillis}ms)")
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Critical error in compareImagesLong: $e")
            } finally {
                // CRITICAL: Always perform cleanup regardless of how we exit
                try {
                    Log.d(ApiConstant.LOG_TAG, "Starting cleanup in compareImagesLong")
                    synchronized(runningJobs) {
                        runningJobs.forEach { job ->
                            try {
                                job.cancel()
                                Log.d(ApiConstant.LOG_TAG, "Cancelled job: $job")
                            } catch (e: Exception) {
                                Log.w(ApiConstant.LOG_TAG, "Error cancelling job: $e")
                            }
                        }
                        runningJobs.clear()
                    }
                    checkForUnreleasedMats()
                    Log.d(ApiConstant.LOG_TAG, "compareImagesLong cleanup completed")
                } catch (e: Exception) {
                    Log.e(ApiConstant.LOG_TAG, "Error during compareImagesLong cleanup: $e")
                }
            }

            Log.d(
                ApiConstant.LOG_TAG,
                "compareImagesLong result at ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms: ${finalResult.first}",
            )
            finalResult
        }

    private fun Point.withRandomOffset(maxOffset: Int = 5): Point {
        val dx = Random.nextInt(-maxOffset, maxOffset + 1)
        val dy = Random.nextInt(-maxOffset, maxOffset + 1)
        return Point(x + dx, y + dy)
    }

    // Check for unreleased Mat objects using global variables
    fun checkForUnreleasedMats() {
        synchronized(resourceLock) {
            // Use global Mat to check address instead of creating new one
            val address = globalTemplateMat?.nativeObj ?: 0
            Log.d(ApiConstant.LOG_TAG, "Global template Mat address: $address")

            // Count active resource slots
            val activeSlots = resourceInUse.count { it }
            Log.d(ApiConstant.LOG_TAG, "Active resource slots: $activeSlots/$MAX_CONCURRENT_COMPARISONS")

            // Log template cache size
            Log.d(ApiConstant.LOG_TAG, "Template cache size: ${templateCache.size}")
        }
    }

    suspend fun loadTemplate(): Boolean =
        withContext(Dispatchers.Default) {
            calculateAndUpdateAllTemplateScales()
            preloadAllTemplates()
            return@withContext true
        }

    // === Template Management Functions (moved from OpencvObject) ===
    private fun getTemplateDimensions(assetPath: String): Size {
        try {
            assets.open(assetPath).use { inputStream ->
                val options =
                    BitmapFactory.Options().apply {
                        inJustDecodeBounds = true // Only decode image bounds, not the full image
                    }
                BitmapFactory.decodeStream(inputStream, null, options)
                // Don't check the return value - check the options instead!
                if (options.outWidth <= 0 || options.outHeight <= 0) {
                    throw IllegalStateException("Failed to decode template dimensions: $assetPath")
                }
                return Size(options.outWidth.toDouble(), options.outHeight.toDouble())
            }
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Error getting template dimensions: $assetPath", e)
            throw e
        }
    }

    private fun getAllTemplateConfigs(): List<TemplateConfig> {
        val configs = mutableListOf<TemplateConfig>()
        // Images used in checkLoadGame
        configs.add(ApiConstant.SMG935F_HOME)
        configs.add(ApiConstant.SMG935F_HOME_GRAY)
        // Images used in checkLoadNewFarm
        configs.add(ApiConstant.SMG935F_START_NEWFARM)
        // Images used in checkBuyChicken
        configs.add(ApiConstant.SMG935F_BUYCHICKEN)
        configs.add(ApiConstant.SMG935F_BUYCHICKEN_DONE)
        configs.add(ApiConstant.SMG935F_KEYBOARD_HAYDAY)
        // Images used in buyChicken
        configs.add(ApiConstant.SMG935F_COOP)
        configs.add(ApiConstant.SMG935F_PUTNAME)
        // Images used in freeEggs
        configs.add(ApiConstant.SMG935F_FREE_EGGS)
        configs.add(ApiConstant.SMG935F_DONE_FREE_EGGS)
        // Images used in freeWheat
        configs.add(ApiConstant.SMG935F_FREE_WHEAT)
        configs.add(ApiConstant.SMG935F_LEVELUP)
        // Images used in checkLevel
        configs.add(ApiConstant.SMG935F_LEVEL1)
        configs.add(ApiConstant.SMG935F_LEVEL2)
        configs.add(ApiConstant.SMG935F_LEVEL3)
        configs.add(ApiConstant.SMG935F_LEVEL4)
        configs.add(ApiConstant.SMG935F_LEVEL5)
        configs.add(ApiConstant.SMG935F_LEVEL6)
        configs.add(ApiConstant.SMG935F_LEVEL7)
        // Images used in buyMachineBreed
        configs.add(ApiConstant.SMG935F_BUYSPC)
        configs.add(ApiConstant.SMG935F_BUYSPC_DONE)
        // Images used in truckTask
        configs.add(ApiConstant.SMG935F_TRUCKTASK1)
        configs.add(ApiConstant.SMG935F_TRUCKTASK2)
        configs.add(ApiConstant.SMG935F_TRUCKTASK3)
        // Images used in checkPointField
        configs.add(ApiConstant.SMG935F_CHECKFIELD)
        // Images used in openShop
        configs.add(ApiConstant.SMG935F_SHOPFIRST)
        configs.add(ApiConstant.SMG935F_LOADINGSHOP)
        // Images used in checkLoadNewFarm
        ApiConstant.SMG935F_NEWFARM.map { configs.add(it) }
        // Images used in freeWheat
        ApiConstant.SMG935F_DONE_FREE_WHEAT.map { configs.add(it) }
        return configs
    }

    fun calculateAndUpdateAllTemplateScales() {
        val templateConfigs = getAllTemplateConfigs()

        for (config in templateConfigs) {
            try {
                // Only calculate if scale is still at default value
                if (config.scale == 1.0) {
                    // Get template dimensions
                    val dimensions = getTemplateDimensions(config.path)

                    // Calculate optimal scale based on template size
                    val optimalScale = calculateDownscaleFactor(dimensions.width, dimensions.height)

                    // Update the TemplateConfig with the calculated scale
                    config.scale = optimalScale

                    Log.d(
                        ApiConstant.LOG_TAG,
                        "Updated scale for ${config.path}: $optimalScale " +
                            "(size: ${dimensions.width}x${dimensions.height})",
                    )
                }
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error calculating scale for ${config.path}", e)
            }
        }
    }

    fun preloadAllTemplates() {
        val configs = getAllTemplateConfigs()

        for (config in configs) {
            try {
                loadTemplate(this, config)
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error preloading template: ${config.path}", e)
            }
        }
    }

    private fun calculateDownscaleFactor(
        width: Double,
        height: Double,
    ): Double {
        val area = width * height

        return when {
            area > 100000 -> 0.25 // Very large templates (e.g., 316×316 or larger)
            area > 40000 -> 0.33 // Large templates (e.g., 200×200 or larger)
            area > 10000 -> 0.5 // Medium templates (e.g., 100×100 or larger)
            area > 2500 -> 0.75 // Small templates (e.g., 50×50 or larger)
            else -> 1.0 // Tiny templates - no downscaling
        }
    }

    // === Image Utility Functions using global variables ===
    fun saveMatAsImage(
        mat: Mat,
        filename: String,
        path: String,
    ) {
        if (mat.empty()) {
            Log.e(ApiConstant.LOG_TAG, "Cannot save empty Mat: $filename")
            return
        }
        synchronized(resourceLock) {
            try {
                // Use global Mat for image processing instead of creating new one
                Core.normalize(mat, globalSaveImageMat!!, 0.0, 255.0, Core.NORM_MINMAX)
                globalSaveImageMat!!.convertTo(globalSaveImageMat!!, CvType.CV_8U)

                // Reuse global bitmap for image saving - check if we can reuse existing bitmap with safe dimension checking
                val saveDimensions = safeBitmapDimensions(globalSaveImageBitmap)
                val requiredWidth = globalSaveImageMat!!.cols()
                val requiredHeight = globalSaveImageMat!!.rows()
                val needsSaveRecreation = globalSaveImageBitmap == null ||
                    globalSaveImageBitmap!!.isRecycled ||
                    saveDimensions == null ||
                    saveDimensions.first != requiredWidth ||
                    saveDimensions.second != requiredHeight

                if (needsSaveRecreation) {
                    safeRecycleBitmap(globalSaveImageBitmap, "globalSaveImageBitmap")
                    globalSaveImageBitmap =
                        createBitmap(globalSaveImageMat!!.cols(), globalSaveImageMat!!.rows())
                }

                // Safe matToBitmap conversion
                val success = safeBitmapOperation(globalSaveImageBitmap, "matToBitmap conversion") { bitmap ->
                    Utils.matToBitmap(globalSaveImageMat!!, bitmap)
                }
                if (!success) {
                    Log.e(ApiConstant.LOG_TAG, "Failed to convert Mat to bitmap for saving: $filename")
                    return
                }

                val mimeType = "image/png"
                val timestamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.US).format(Date())
                val uniqueFilename = "${filename}_$timestamp.png"
                val values =
                    ContentValues().apply {
                        put(MediaStore.Images.Media.DISPLAY_NAME, uniqueFilename)
                        put(MediaStore.Images.Media.MIME_TYPE, mimeType)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            put(MediaStore.Images.Media.RELATIVE_PATH, path.trimStart('/'))
                            put(MediaStore.Images.Media.IS_PENDING, 1)
                        } else {
                            // For devices before Android Q, use an absolute file path.
                            val folder = File(Environment.getExternalStorageDirectory(), path)
                            if (!folder.exists() && !folder.mkdirs()) {
                                Log.e(
                                    ApiConstant.LOG_TAG,
                                    "Failed to create folder: ${folder.absolutePath}",
                                )
                                return
                            }
                            Log.d(
                                ApiConstant.LOG_TAG,
                                "folder: ${folder.absolutePath}, filename: $uniqueFilename",
                            )
                            val uniqueFile = File(folder, uniqueFilename)
                            Log.d(ApiConstant.LOG_TAG, "uniqueFile: ${uniqueFile.absolutePath}")
                            put(MediaStore.Images.Media.DATA, uniqueFile.absolutePath)
                        }
                    }

                val uri =
                    contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
                uri?.let {
                    contentResolver.openOutputStream(uri)?.use { out ->
                        if (globalSaveImageBitmap!!.compress(Bitmap.CompressFormat.PNG, 100, out)) {
                            Log.d(ApiConstant.LOG_TAG, "Saved image to $uri using global resources")
                        } else {
                            Log.e(ApiConstant.LOG_TAG, "Failed to compress and save image.")
                        }
                    } ?: run {
                        Log.e(ApiConstant.LOG_TAG, "Failed to open output stream for URI: $uri")
                    }
                } ?: Log.e(ApiConstant.LOG_TAG, "Failed to insert image into MediaStore")
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error saving Mat as image: $filename", e)
            }
        }
    }

    // VirtualDisplay screen capture method - hybrid approach: persistent VirtualDisplay with improved callback filtering
    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun getSourceBitmap(
        screenWidth: Int,
        screenHeight: Int,
        densityDpi: Int,
    ): Boolean =
        withContext(Dispatchers.IO) {
            requireNotNull(mediaProjection) { "MediaProjection is null" }

            // Increment total capture requests for monitoring
            totalCaptureRequests++

            // Apply cooldown to reduce VirtualDisplay frequency
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastCaptureTime < MIN_CAPTURE_INTERVAL) {
                captureSkipCount++
                Log.d(ApiConstant.LOG_TAG,
                    "Capture cooldown active: ${currentTime - lastCaptureTime}ms < ${MIN_CAPTURE_INTERVAL}ms " +
                    "(skipped: ${captureSkipCount}/${totalCaptureRequests})")
                return@withContext false
            }
            lastCaptureTime = currentTime

            // Prevent multiple concurrent captures
            if (!isCapturing.compareAndSet(false, true)) {
                Log.w(ApiConstant.LOG_TAG, "Screen capture already in progress, skipping")
                return@withContext false
            }

            Log.d(ApiConstant.LOG_TAG, "getSourceBitmap: Starting controlled capture ${screenWidth}x${screenHeight}")

            try {
                // Setup persistent resources with improved callback filtering
                setupPersistentCaptureResources(screenWidth, screenHeight, densityDpi)

                // Perform controlled single capture with timeout
                val success = withTimeout(5000L) {
                    performControlledCapture()
                }

                if (success) {
                    Log.d(ApiConstant.LOG_TAG, "Successfully captured bitmap using hybrid VirtualDisplay approach")
                }

                return@withContext success

            } catch (e: Exception) {
                when (e) {
                    is TimeoutCancellationException -> {
                        Log.e(ApiConstant.LOG_TAG, "Controlled screen capture timed out after 5 seconds", e)
                        captureCompletionCallback = null
                    }
                    else -> {
                        Log.e(ApiConstant.LOG_TAG, "Error in controlled screen capture", e)
                        virtualDisplayState = VirtualDisplayState.ERROR
                    }
                }
                return@withContext false
            } finally {
                // Don't destroy VirtualDisplay - keep it persistent but mark capture as complete
                isCapturing.set(false)
                captureCompletionCallback = null
            }
        }

    private fun setupPersistentCaptureResources(screenWidth: Int, screenHeight: Int, densityDpi: Int) {
        // CRITICAL: Create HandlerThread FIRST before using it
        if (globalHandlerThread == null) {
            Log.d(ApiConstant.LOG_TAG, "Creating HandlerThread for hybrid approach")
            globalHandlerThread = HandlerThread("HybridCaptureThread").apply { start() }
        }

        if (globalImageReader == null) {
            Log.d(ApiConstant.LOG_TAG, "Creating ImageReader for hybrid approach")
            globalImageReader = ImageReader.newInstance(screenWidth, screenHeight, PixelFormat.RGBA_8888, 2)

            // Set up improved callback with better filtering
            globalImageReader!!.setOnImageAvailableListener({ reader ->
                handleImageAvailableWithImprovedFiltering(reader)
            }, getOrCreateHandler())
        }

        // Keep VirtualDisplay persistent but with improved callback handling
        ensurePersistentVirtualDisplayWithImprovedFiltering(screenWidth, screenHeight, densityDpi)
    }

    private fun ensurePersistentVirtualDisplayWithImprovedFiltering(screenWidth: Int, screenHeight: Int, densityDpi: Int) {
        try {
            when (virtualDisplayState) {
                VirtualDisplayState.PAUSED -> {
                    Log.d(ApiConstant.LOG_TAG, "Creating persistent VirtualDisplay with improved filtering")
                    globalVirtualDisplay = mediaProjection!!.createVirtualDisplay(
                        "HybridScreenCapture",
                        screenWidth,
                        screenHeight,
                        densityDpi,
                        DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY or DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
                        globalImageReader!!.surface,
                        null,
                        getOrCreateHandler(),
                    )
                    virtualDisplayState = VirtualDisplayState.ACTIVE
                    Log.d(ApiConstant.LOG_TAG, "Persistent VirtualDisplay created successfully with improved filtering")
                }
                VirtualDisplayState.ACTIVE -> {
                    Log.d(ApiConstant.LOG_TAG, "VirtualDisplay already persistent and active")
                }
                VirtualDisplayState.ERROR -> {
                    Log.w(ApiConstant.LOG_TAG, "VirtualDisplay in error state - attempting recovery")
                    // Clean up error state first
                    try {
                        globalVirtualDisplay?.release()
                    } catch (e: Exception) {
                        Log.w(ApiConstant.LOG_TAG, "Error cleaning up failed VirtualDisplay: ${e.message}")
                    }

                    // Create new persistent VirtualDisplay
                    globalVirtualDisplay = mediaProjection!!.createVirtualDisplay(
                        "RecoveryHybridCapture",
                        screenWidth,
                        screenHeight,
                        densityDpi,
                        DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY or DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
                        globalImageReader!!.surface,
                        null,
                        getOrCreateHandler(),
                    )
                    virtualDisplayState = VirtualDisplayState.ACTIVE
                    Log.d(ApiConstant.LOG_TAG, "Persistent VirtualDisplay recovered successfully")
                }
            }
        } catch (e: Exception) {
            virtualDisplayState = VirtualDisplayState.ERROR
            Log.e(ApiConstant.LOG_TAG, "Error ensuring persistent VirtualDisplay with improved filtering", e)
            throw e
        }
    }

    // Note: Removed pauseVirtualDisplayAfterCapture - using persistent VirtualDisplay with improved filtering
    // VirtualDisplay now stays active throughout the service lifecycle with better callback management

    private fun getOrCreateHandler(): Handler {
        return Handler(globalHandlerThread!!.looper)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun performControlledCapture(): Boolean = suspendCancellableCoroutine { cont ->
        Log.d(ApiConstant.LOG_TAG, "Setting up controlled capture callback")

        // Set up one-time completion callback
        captureCompletionCallback = { success ->
            Log.d(ApiConstant.LOG_TAG, "Controlled capture completed: $success")

            if (!cont.isCompleted) {
                cont.resume(success, null)
            }
            // Clear callback after use
            captureCompletionCallback = null
        }

        // Set cancellation handler
        cont.invokeOnCancellation {
            Log.w(ApiConstant.LOG_TAG, "Controlled capture cancelled or timed out")
            captureCompletionCallback = null
        }

        // Trigger capture by requesting a frame
        triggerSingleCapture()
    }

    private fun handleImageAvailableWithImprovedFiltering(reader: ImageReader) {
        // Improved filtering: Check VirtualDisplay state first
        if (virtualDisplayState != VirtualDisplayState.ACTIVE) {
            // Silently drain the image without logging to reduce spam
            try {
                reader.acquireLatestImage()?.close()
            } catch (e: Exception) {
                // Ignore
            }
            return
        }

        // Enhanced filtering: Only process if we're actively expecting a capture
        if (!isCapturing.get() || captureCompletionCallback == null) {
            // Silently drain the image without logging to reduce callback spam
            try {
                reader.acquireLatestImage()?.close()
            } catch (e: Exception) {
                // Ignore
            }
            return
        }

        Log.d(ApiConstant.LOG_TAG, "Processing expected ImageReader callback")

        try {
            val image = reader.acquireLatestImage()
            if (image != null) {
                try {
                    Log.d(ApiConstant.LOG_TAG, "Converting captured image to bitmap")

                    // Convert image to bitmap using global resources
                    synchronized(resourceLock) {
                        safeRecycleBitmap(globalTempBitmap, "globalTempBitmap")
                        globalTempBitmap = imageToBitmap(image)

                        // Create a proper copy for globalSourceBitmap with safe bitmap access
                        safeRecycleBitmap(globalSourceBitmap, "globalSourceBitmap")
                        if (globalTempBitmap != null && !globalTempBitmap!!.isRecycled) {
                            val config = globalTempBitmap!!.config ?: Bitmap.Config.ARGB_8888
                            globalSourceBitmap = globalTempBitmap!!.copy(config, false)

                            // Safe bitmap dimensions logging
                            val dimensions = safeBitmapDimensions(globalSourceBitmap)
                            if (dimensions != null) {
                                Log.d(ApiConstant.LOG_TAG, "Successfully created bitmap: ${dimensions.first}x${dimensions.second}")
                            } else {
                                Log.w(ApiConstant.LOG_TAG, "Created bitmap but cannot get dimensions safely")
                            }
                        } else {
                            Log.e(ApiConstant.LOG_TAG, "Failed to create bitmap - globalTempBitmap is null or recycled")
                            globalSourceBitmap = null
                        }
                    }

                    // Notify completion
                    captureCompletionCallback?.invoke(globalSourceBitmap != null)

                } finally {
                    image.close()
                }
            } else {
                Log.w(ApiConstant.LOG_TAG, "ImageReader callback triggered but image is null")
                captureCompletionCallback?.invoke(false)
            }
        } catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Error processing image in controlled callback", e)
            captureCompletionCallback?.invoke(false)
        }
    }

    private fun triggerSingleCapture() {
        Log.d(ApiConstant.LOG_TAG, "Triggering single screen capture")
        // The VirtualDisplay should automatically capture when the screen content changes
        // We can also try to force a capture by invalidating the surface
        try {
            globalVirtualDisplay?.surface?.let { surface ->
                // Force surface to update by requesting a frame
                Log.d(ApiConstant.LOG_TAG, "Requesting frame from VirtualDisplay surface")
            }
        } catch (e: Exception) {
            Log.w(ApiConstant.LOG_TAG, "Error triggering capture: ${e.message}")
        }
    }





    private fun imageToBitmap(image: android.media.Image): Bitmap {
        var buffer: java.nio.ByteBuffer? = null
        var plane: android.media.Image.Plane? = null

        try {
            synchronized(resourceLock) {
                plane = image.planes[0]
                buffer = plane!!.buffer
                val pixelStride = plane!!.pixelStride
                val rowStride = plane!!.rowStride
                val rowPadding = rowStride - pixelStride * image.width

                // Reuse global bitmap for image conversion - check if we can reuse existing bitmap
                val requiredWidth = image.width + rowPadding / pixelStride
                val requiredHeight = image.height

                // Safe bitmap dimension checking for globalImageConversionBitmap
                val currentDimensions = safeBitmapDimensions(globalImageConversionBitmap)
                val needsRecreation = globalImageConversionBitmap == null ||
                    globalImageConversionBitmap!!.isRecycled ||
                    currentDimensions == null ||
                    currentDimensions.first != requiredWidth ||
                    currentDimensions.second != requiredHeight

                if (needsRecreation) {
                    safeRecycleBitmap(globalImageConversionBitmap, "globalImageConversionBitmap")
                    globalImageConversionBitmap = createBitmap(requiredWidth, requiredHeight)
                }

                // Reset buffer position before copying
                buffer!!.rewind()
                globalImageConversionBitmap!!.copyPixelsFromBuffer(buffer!!)

                // Reuse global temp bitmap for final result - check if we can reuse existing bitmap with safe dimension checking
                val tempDimensions = safeBitmapDimensions(globalTempConversionBitmap)
                val needsTempRecreation = globalTempConversionBitmap == null ||
                    globalTempConversionBitmap!!.isRecycled ||
                    tempDimensions == null ||
                    tempDimensions.first != image.width ||
                    tempDimensions.second != image.height

                if (needsTempRecreation) {
                    safeRecycleBitmap(globalTempConversionBitmap, "globalTempConversionBitmap")
                    globalTempConversionBitmap = Bitmap.createBitmap(globalImageConversionBitmap!!, 0, 0, image.width, image.height)
                } else {
                    // Reuse existing bitmap by copying pixels
                    val canvas = android.graphics.Canvas(globalTempConversionBitmap!!)
                    canvas.drawBitmap(
                        globalImageConversionBitmap!!,
                        android.graphics.Rect(0, 0, image.width, image.height),
                        android.graphics.Rect(0, 0, image.width, image.height),
                        null,
                    )
                }

                return globalTempConversionBitmap!!
            }
        } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error converting image to bitmap", e)
                // Clean up on error to prevent leaks
                safeRecycleBitmap(globalImageConversionBitmap, "globalImageConversionBitmap")
                globalImageConversionBitmap = null
                safeRecycleBitmap(globalTempConversionBitmap, "globalTempConversionBitmap")
                globalTempConversionBitmap = null
                throw e
        } finally {
            // Final cleanup outside synchronized block
            buffer = null
            plane = null
        }
    }

    suspend fun getInformationStorage(): Int = withContext(Dispatchers.Default) {
        try {
            val result = mutableMapOf<String, Int>()
            val fullBitmap = getSourceBitmap(GlobalVariabel.screenWidthReal,GlobalVariabel.screenHeightReal,560)
            if (!fullBitmap) {
                Log.e(ApiConstant.LOG_TAG, "Failed to capture bitmap for template comparison")
                return@withContext ApiConstant.API_FAILED
            }

            // Then use synchronized block only for global resource operations with safe bitmap access
            synchronized(resourceLock) {
                val tmpSuccess = safeBitmapOperation(globalSourceBitmap, "bitmapToMat conversion") { bitmap ->
                    Utils.bitmapToMat(bitmap, globalScreenCaptureMat!!)
                }
                if (!tmpSuccess) {
                    Log.e(ApiConstant.LOG_TAG, "Failed to convert bitmap to Mat - bitmap was recycled")
                    return@withContext ApiConstant.API_FAILED
                }
            }

            // Danh sách các template path và tên
            val templates = mapOf(
                "Plank" to ApiConstant.SMG935F_PLANK,
                "Duct Tape" to ApiConstant.SMG935F_DUCT,
                "Bolt" to ApiConstant.SMG935F_BOLT,
                "Nail" to ApiConstant.SMG935F_NAIL,
                "Screw" to ApiConstant.SMG935F_SCREW,
                "Wood Panel" to ApiConstant.SMG935F_WOODPANEL
            )

            // 6 vùng rect (100x65)
            val rects = listOf(
                ApiConstant.RECT_ITEMS1,
                ApiConstant.RECT_ITEMS2,
                ApiConstant.RECT_ITEMS3,
                ApiConstant.RECT_ITEMS4,
                ApiConstant.RECT_ITEMS5,
                ApiConstant.RECT_ITEMS6,
                ApiConstant.RECT_ITEMS7,
                ApiConstant.RECT_ITEMS8,
                ApiConstant.RECT_QUANTITY_STORAGE
            )
            var i = 0
            for (rect in rects) {
                i++
                Log.d(ApiConstant.LOG_TAG,"Storage: $i - START")
                var bestMatch: String? = null
                val slotIndex = acquireResourceSlot()
                if (slotIndex == null) {
                    Log.w(ApiConstant.LOG_TAG, "No available resource slot for template: $rect")
                    continue
                }

                try {
                    // Use global resources instead of creating new ones
                    val screenCopy = globalScreenMats[slotIndex]!!
                    globalScreenCaptureMat!!.copyTo(screenCopy)
                    Log.d(
                        ApiConstant.LOG_TAG,
                        "templatePaths.forEach matching $rect using slot $slotIndex ${((System.nanoTime() - GlobalVariabel.timeNano) / 1_000_000.0)} ms",
                    )
                    if(i == 9){
                        val quantity = extractQuantityFromImage(this@OpencvService,screenCopy,rect,slotIndex)
//                        val quantity = extractQuantityWithMLKit(screenCopy,rect,slotIndex)
                        Log.w(ApiConstant.LOG_TAG,"Match: STORAGE quantity $quantity")
                    }else{
                        continue
                        for ((itemName, templatePath) in templates) {
                            Log.d(ApiConstant.LOG_TAG,"Storage: $i, $rect, $itemName, $templatePath")
//                            val match =
//                                performTemplateMatchingWithGlobalResources(
//                                    TemplateConfig(templatePath, Rect(rect.x+5,rect.y+15,28,30)),
//                                    screenCopy,
//                                    ApiConstant.RATIO_IMAGE_MID,
//                                    false,
//                                    slotIndex,
//                                )
//                            match?.let {
//                                Log.d(ApiConstant.LOG_TAG, "MATCH FOUND: $templatePath")
//                                bestMatch = itemName
//                            }
//                            if (bestMatch != null) {
                                Log.d(ApiConstant.LOG_TAG,"Match: $bestMatch")
                                // OCR để lấy số lượng
                                val quantity = extractQuantityFromImage(this@OpencvService,screenCopy,Rect(rect.x + ApiConstant.RECT_QUANTITY_ITEMS.x, rect.y + ApiConstant.RECT_QUANTITY_ITEMS.y, ApiConstant.RECT_QUANTITY_ITEMS.width, ApiConstant.RECT_QUANTITY_ITEMS.height),slotIndex)
//                            val quantity = extractQuantityFromImage(this@OpencvService,screenCopy,rect,slotIndex)
//                                val quantity = extractQuantityWithMLKit(screenCopy,Rect(rect.x + ApiConstant.RECT_QUANTITY_ITEMS.x, rect.y + ApiConstant.RECT_QUANTITY_ITEMS.y, ApiConstant.RECT_QUANTITY_ITEMS.width, ApiConstant.RECT_QUANTITY_ITEMS.height),slotIndex)
                                if (quantity != null) {
                                    result[bestMatch!!] = quantity
                                    Log.w(ApiConstant.LOG_TAG,"Storage: $bestMatch: $quantity")
                                }
                                Log.w(ApiConstant.LOG_TAG,"Match: $bestMatch: $quantity")
                                break
//                            }
//                            delay(50)
                        }
                    }
                }catch (e: Exception) {
                    if (e is CancellationException) {
                        Log.d(ApiConstant.LOG_TAG, "Template matching cancelled: $rect slot $slotIndex")
                    } else {
                        Log.e(
                            ApiConstant.LOG_TAG,
                            "Storage error: $rect slot $slotIndex - ${e.localizedMessage}",
                        )
                    }
                } finally {
                    releaseResourceSlot(slotIndex)
                }
            }
            return@withContext ApiConstant.API_SUCCESS
        }catch (e: Exception) {
            Log.e(ApiConstant.LOG_TAG, "Critical error in compareListImage: $e")
            return@withContext ApiConstant.API_FAILED
        } finally {
            // CRITICAL: Always cleanup resources
            try {
                checkForUnreleasedMats()
                Log.d(ApiConstant.LOG_TAG, "compareListImage cleanup completed")
            } catch (e: Exception) {
                Log.e(ApiConstant.LOG_TAG, "Error during compareListImage cleanup: $e")
            }
        }
    }

    private suspend fun extractQuantityFromImage(context: Context,itemMat: Mat, rect: Rect, slotIndex: Int): Int? {
        Log.d(ApiConstant.LOG_TAG,"Match: START extract quantity")
        val croppedItem = globalRoiMats[slotIndex]!!
        itemMat.submat(rect).copyTo(croppedItem)
        val number = detectNumberFromScreen(croppedItem, slotIndex)
        Log.d(ApiConstant.LOG_TAG, "Number Found: $number")
        Log.w(ApiConstant.LOG_TAG, "Match: quantity null")
        return null
    }

    data class DigitMatch(val digit: Int, val point: Point, val score: Double)

    fun detectNumberFromScreenTest(screenMat: Mat, slotIndex: Int): String {
        Log.d(ApiConstant.LOG_TAG,"Match: Start detectNumberFromScreen ${screenMat.width()}x${screenMat.height()}")
        val matches = mutableListOf<DigitMatch>()
        val threshold = 0.97  // matching score threshold
        val templates = mapOf(
            99 to ApiConstant.SMG935F_DGIS_DELIMITER_MASK,
//            0 to ApiConstant.SMG935F_DGIS_0,
//            1 to ApiConstant.SMG935F_DGIS_1,
//            2 to ApiConstant.SMG935F_DGIS_2,
//            3 to ApiConstant.SMG935F_DGIS_3,
//            4 to ApiConstant.SMG935F_DGIS_4,
            5 to ApiConstant.SMG935F_DGIS_5,
//            6 to ApiConstant.SMG935F_DGIS_6,
            7 to ApiConstant.SMG935F_DGIS_7,
//            8 to ApiConstant.SMG935F_DGIS_8,
//            9 to ApiConstant.SMG935F_DGIS_9
        )
        val result = globalResultMats[slotIndex]!!
        var leftMat = globalScaledRoiMats[slotIndex]!!
        var rightMat = globalScreenGrayMats[slotIndex]!!
        val templateGray = globalTemplateGrayMats[slotIndex]!!
        val maskMat = globalTemplateMaskMats[slotIndex]!!
        val resultRight = globalTemplateCloneMats[slotIndex]!!

        for ((digit, template) in templates) {
            try{
                val cachedTemplate = loadTemplate(this, TemplateConfig(template,Rect()))
                cachedTemplate.copyTo(templateGray)
                // Release the clone immediately to prevent memory leak
                cachedTemplate.release()

                val templateMask = when(digit){
                    99 -> ApiConstant.SMG935F_DGIS_DELIMITER_MASK
                    0 -> ApiConstant.SMG935F_DGIS_0_MASK
                    1 -> ApiConstant.SMG935F_DGIS_1_MASK
                    2 -> ApiConstant.SMG935F_DGIS_2_MASK
                    3 -> ApiConstant.SMG935F_DGIS_3_MASK
                    4 -> ApiConstant.SMG935F_DGIS_4_MASK
                    5 -> ApiConstant.SMG935F_DGIS_5_MASK
                    6 -> ApiConstant.SMG935F_DGIS_6_MASK
                    7 -> ApiConstant.SMG935F_DGIS_7_MASK
                    8 -> ApiConstant.SMG935F_DGIS_8_MASK
                    9 -> ApiConstant.SMG935F_DGIS_9_MASK
                    else -> ApiConstant.SMG935F_DGIS_DELIMITER_MASK
                }
                val cachedTemplateMask = loadTemplate(this, TemplateConfig(templateMask,Rect()))
                cachedTemplateMask.copyTo(maskMat)
                // Release the clone immediately to prevent memory leak
                cachedTemplateMask.release()
                if(template == ApiConstant.SMG935F_DGIS_DELIMITER){
                    Imgproc.matchTemplate(screenMat, templateGray, result, Imgproc.TM_CCORR_NORMED, maskMat)
//                    Core.normalize(result, result, 0.0, 1.0, Core.NORM_MINMAX, -1)
                    val mmr = Core.minMaxLoc(result)
                    if (mmr.maxVal >= threshold) {
                        val delimiterPoint = mmr.maxLoc  // x, y của dấu '/'
                        Log.d("MATCH", "Delimiter / at $delimiterPoint with score ${mmr.maxVal}")
                    }
                    val slashX = mmr.maxLoc.x.toInt()

                    leftMat = screenMat.submat(0, screenMat.rows(), 0, slashX)
                    rightMat = screenMat.submat(0, screenMat.rows(), slashX + result.cols(), screenMat.cols())
                }else{
                    Imgproc.matchTemplate(screenMat, templateGray, result, Imgproc.TM_CCORR_NORMED, maskMat)
//                    Core.normalize(result, result, 0.0, 1.0, Core.NORM_MINMAX, -1)
                    val used = getOrResetUsedMat(slotIndex, result.size())
                    Log.w(ApiConstant.LOG_TAG, "Size ${result.size()}")

                    for (y in 0 until result.rows()) {
                        for (x in 0 until result.cols()) {
                            val score = result.get(y, x)[0]
                            Log.d(ApiConstant.LOG_TAG,"Score: $x x $y - $score")
                            if (score >= threshold && used.get(y, x)[0] == 0.0) {
                                Log.w(ApiConstant.LOG_TAG,"Score match: $digit $x x $y - $score")
                                matches.add(DigitMatch(digit, Point(x, y), score))
                                val rangeX = maxOf(0, x - templateGray.cols()/2)..minOf(result.cols() - 1, x + templateGray.cols()/2)
                                val rangeY = maxOf(0, y - templateGray.rows()/2)..minOf(result.rows() - 1, y + templateGray.rows()/2)

                                for (yy in rangeY) {
                                    for (xx in rangeX) {
                                        used.put(yy, xx, 255.0)
                                    }
                                }
                            }
                        }
                    }
                }
            }catch (e: Exception) {
                if (e is CancellationException) {
                    Log.d(ApiConstant.LOG_TAG, "Template matching cancelled: $template slot $slotIndex")
                } else {
                    Log.e(
                        ApiConstant.LOG_TAG,
                        "compareListImage error: $template slot $slotIndex - ${e.localizedMessage}",
                    )
                }
                if(template == ApiConstant.SMG935F_DGIS_DELIMITER){
                    return ""
                }
            }
        }
        var text = ""
        matches.forEach { match ->
            text += match.digit
        }
        Log.w(ApiConstant.LOG_TAG, "Result: $matches | $text")
        return text
//        fun filterSameRow(matches: List<DigitMatch>): List<DigitMatch> {
//            val baseY: Double = matches.minByOrNull { it.point.y }?.point?.y?.toDouble() ?: 0.0
//            Log.d("DEBUG", "Base Y = $baseY")
//
//            val result = matches.filter { match ->
//                val deltaY = abs(match.point.y.toDouble() - baseY)
//                val inRange = deltaY <= 6.0
//                Log.d("DEBUG", "Checking match y=${match.point.y} -> deltaY=$deltaY -> inRange=$inRange")
//                inRange
//            }
//
//            Log.d("DEBUG", "Filtered ${result.size} matches")
//            return result
//        }
//
//
//        val sameRow = filterSameRow(matches)
//        val filtered = suppressOverlappingMatches(sameRow, minDist = 15.0)
//
//        return filtered.sortedBy { it.point.x }
//            .joinToString("") { it.digit.toString() }

    }

    private suspend fun detectNumberFromScreen(screenMat: Mat, slotIndex: Int): String {
        Log.d(ApiConstant.LOG_TAG, "Match: Start detectNumberFromScreen ${screenMat.width()}x${screenMat.height()}")

        val matches = mutableListOf<DigitMatch>()
        val threshold = 0.97
        val templates = mapOf(
            99 to ApiConstant.SMG935F_DGIS_DELIMITER_MASK,
//            0 to ApiConstant.SMG935F_DGIS_0,
            1 to ApiConstant.SMG935F_DGIS_1,
//            2 to ApiConstant.SMG935F_DGIS_2,
//            3 to ApiConstant.SMG935F_DGIS_3,
//            4 to ApiConstant.SMG935F_DGIS_4,
            5 to ApiConstant.SMG935F_DGIS_5,
//            6 to ApiConstant.SMG935F_DGIS_6,
            7 to ApiConstant.SMG935F_DGIS_7,
            8 to ApiConstant.SMG935F_DGIS_8,
//            9 to ApiConstant.SMG935F_DGIS_9,
        )

        val result = globalResultMats[slotIndex]!!
        val templateGray = globalTemplateGrayMats[slotIndex]!!
        val maskMat = globalTemplateMaskMats[slotIndex]!!

        var leftMat: Mat? = null
        var rightMat: Mat? = null
        var slashX = -1

        for ((digit, template) in templates) {
            try {
                val cachedTemplate = loadTemplate(this, TemplateConfig(template, Rect()))
                cachedTemplate.copyTo(templateGray)
                cachedTemplate.release()

//                val templateMask = when (digit) {
//                    99 -> ApiConstant.SMG935F_DGIS_DELIMITER_MASK
//                    0 -> ApiConstant.SMG935F_DGIS_0_MASK
//                    1 -> ApiConstant.SMG935F_DGIS_1_MASK
//                    2 -> ApiConstant.SMG935F_DGIS_2_MASK
//                    3 -> ApiConstant.SMG935F_DGIS_3_MASK
//                    4 -> ApiConstant.SMG935F_DGIS_4_MASK
//                    5 -> ApiConstant.SMG935F_DGIS_5_MASK
//                    6 -> ApiConstant.SMG935F_DGIS_6_MASK
//                    7 -> ApiConstant.SMG935F_DGIS_7_MASK
//                    8 -> ApiConstant.SMG935F_DGIS_8_MASK
//                    9 -> ApiConstant.SMG935F_DGIS_9_MASK
//                    else -> ApiConstant.SMG935F_DGIS_DELIMITER_MASK
//                }
//                val cachedTemplateMask = loadTemplate(this, TemplateConfig(templateMask, Rect()))
//                cachedTemplateMask.copyTo(maskMat)
//                cachedTemplateMask.release()

//                saveMatAsImage(screenMat, "Screen", ApiConstant.IMG_PATH)
//                delay(2000)
//                saveMatAsImage(templateGray, "Template", ApiConstant.IMG_PATH)
//                delay(2000)
//                saveMatAsImage(maskMat, "Mask", ApiConstant.IMG_PATH)
//                delay(2000)


                Imgproc.matchTemplate(screenMat, templateGray, result, Imgproc.TM_CCORR_NORMED)
                Core.normalize(result, result, 0.0, 1.0, Core.NORM_MINMAX, -1)
                val used = getOrResetUsedMat(slotIndex, result.size())

                Log.d(ApiConstant.LOG_TAG, "Size: ${result.size()} | ${result.rows()}x${result.cols()}")
                for (y in 0 until result.rows()) {
                    for (x in 0 until result.cols()) {
                        val score = result.get(y, x)[0]

                        if (score >= threshold  && used.get(y, x)[0] == 0.0) {
                            matches.add(DigitMatch(digit, Point(x, y), score))

                            val suppressX = (templateGray.cols() * 1.5).toInt()
                            val suppressY = (templateGray.rows() * 1.5).toInt()

                            val rangeX = maxOf(0, x - suppressX / 2)..minOf(result.cols() - 1, x + suppressX / 2)
                            val rangeY = maxOf(0, y - suppressY / 2)..minOf(result.rows() - 1, y + suppressY / 2)
                            Log.d(ApiConstant.LOG_TAG, "RangeX: $rangeX, RangeY: $rangeY")
                            for (yy in rangeY) {
                                for (xx in rangeX) {
                                    used.put(yy, xx, 255.0)
                                }
                            }
                        }
                    }
                }

            } catch (e: Exception) {
                if (e is CancellationException) {
                    Log.d(ApiConstant.LOG_TAG, "Template matching cancelled: $template slot $slotIndex")
                } else {
                    Log.e(ApiConstant.LOG_TAG, "compareListImage error: $template slot $slotIndex - ${e.localizedMessage}")
                }
                if (digit == 99) return ""
            }
        }

        // Sort and log
        matches.sortBy { it.point.x }
        matches.forEach {
            Log.d(ApiConstant.LOG_TAG, "Match digit=${it.digit} at x=${it.point.x}, score=${it.score}")
        }
        val text = matches.joinToString(separator = "") {
            if (it.digit == 99) "/" else it.digit.toString()
        }
        Log.w(ApiConstant.LOG_TAG, "Result: $matches | $text")
        return text
    }

    fun getOrResetUsedMat(slot: Int, size: Size): Mat {
        val current = globalUsedMats[slot]
        if (current == null || current.empty() || current.size() != size) {
            current?.release()
            val newMat = Mat.zeros(size, CvType.CV_8U)
            globalUsedMats[slot] = newMat
            return newMat
        }
        current.setTo(Scalar(0.0))
        return current
    }

}
