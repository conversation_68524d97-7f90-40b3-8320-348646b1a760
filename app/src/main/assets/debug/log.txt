2025-06-24 10:44:31.001  2781-2781  NaApp                   com.dragon.clicker                   D  onStop activated
2025-06-24 10:44:38.374  2781-2781  NaApp                   com.dragon.clicker                   D  Hayday button clicked, isRunning=false
2025-06-24 10:44:38.375  2781-2781  NaApp                   com.dragon.clicker                   D  Hayday button clicked, isRunning=false
2025-06-24 10:44:38.377  2781-2781  NaApp                   com.dragon.clicker                   D  Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
2025-06-24 10:44:38.377  2781-2781  NaApp                   com.dragon.clicker                   D  Accessibility service enabled: true
2025-06-24 10:44:38.377  2781-2781  NaApp                   com.dragon.clicker                   D  Accessibility service enabled: true
2025-06-24 10:44:38.438  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService onStartCommand() called
2025-06-24 10:44:38.438  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService resultCode: -1, resultData: Intent { (has extras) }
2025-06-24 10:44:38.439  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService: Activity.RESULT_OK = -1
2025-06-24 10:44:38.439  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService: Creating MediaProjection
2025-06-24 10:44:38.439  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService: MediaProjection already exists: android.media.projection.MediaProjection@4c933c6
2025-06-24 10:44:38.439  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService: Setting up notification and starting foreground
2025-06-24 10:44:38.442  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService started successfully
2025-06-24 10:44:40.450  2781-2781  NaApp                   com.dragon.clicker                   D  Starting Hayday service...
2025-06-24 10:44:40.455  2781-2781  NaApp                   com.dragon.clicker                   D  Network available: true
2025-06-24 10:44:40.458  2781-2781  NaApp                   com.dragon.clicker                   D  Internet available: true
2025-06-24 10:44:40.461  2781-2781  NaApp                   com.dragon.clicker                   D  Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
2025-06-24 10:44:40.461  2781-2781  NaApp                   com.dragon.clicker                   D  Accessibility service enabled: true
2025-06-24 10:44:40.461  2781-2781  NaApp                   com.dragon.clicker                   D  OpencvService instance: true
2025-06-24 10:44:40.464  2781-2781  NaApp                   com.dragon.clicker                   D  Available memory before starting Hayday service: 1713 MB
2025-06-24 10:44:40.491  2781-2781  NaApp                   com.dragon.clicker                   D  Hayday service start requested
2025-06-24 10:44:40.597  2781-4986  NaApp                   com.dragon.clicker                   D  HaydayService initialized
2025-06-24 10:44:40.597  2781-4986  NaApp                   com.dragon.clicker                   D  Root access granted
2025-06-24 10:44:40.614  2781-4990  NaApp                   com.dragon.clicker                   D  getSourceBitmap: Starting controlled capture 640x360
2025-06-24 10:44:40.615  2781-4990  NaApp                   com.dragon.clicker                   D  Creating HandlerThread for hybrid approach
2025-06-24 10:44:40.615  2781-4990  NaApp                   com.dragon.clicker                   D  Creating ImageReader for hybrid approach
2025-06-24 10:44:40.642  2781-4990  NaApp                   com.dragon.clicker                   D  Creating persistent VirtualDisplay with improved filtering
2025-06-24 10:44:40.661  2781-4990  NaApp                   com.dragon.clicker                   D  Persistent VirtualDisplay created successfully with improved filtering
2025-06-24 10:44:40.677  2781-4990  NaApp                   com.dragon.clicker                   D  Setting up controlled capture callback
2025-06-24 10:44:40.678  2781-4990  NaApp                   com.dragon.clicker                   D  Triggering single screen capture
2025-06-24 10:44:40.678  2781-4990  NaApp                   com.dragon.clicker                   D  Requesting frame from VirtualDisplay surface
2025-06-24 10:44:40.736  2781-5189  NaApp                   com.dragon.clicker                   D  Processing expected ImageReader callback
2025-06-24 10:44:40.736  2781-5189  NaApp                   com.dragon.clicker                   D  Converting captured image to bitmap
2025-06-24 10:44:40.737  2781-5189  NaApp                   com.dragon.clicker                   W  ⚠️ Cannot get dimensions of recycled or null bitmap
2025-06-24 10:44:40.739  2781-5189  NaApp                   com.dragon.clicker                   W  ⚠️ Cannot get dimensions of recycled or null bitmap
2025-06-24 10:44:40.759  2781-5189  NaApp                   com.dragon.clicker                   D  Successfully created bitmap: 640x360
2025-06-24 10:44:40.760  2781-5189  NaApp                   com.dragon.clicker                   D  Controlled capture completed: true
2025-06-24 10:44:40.761  2781-4990  NaApp                   com.dragon.clicker                   D  Successfully captured bitmap using hybrid VirtualDisplay approach
2025-06-24 10:44:40.797  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 1 - START
2025-06-24 10:44:40.800  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {150, 65, 100x65} using slot 0 1.4759187924982E7 ms
2025-06-24 10:44:40.801  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 2 - START
2025-06-24 10:44:40.803  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {250, 65, 100x65} using slot 0 1.4759190980367E7 ms
2025-06-24 10:44:40.803  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 3 - START
2025-06-24 10:44:40.805  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {150, 130, 100x65} using slot 0 1.475919262302E7 ms
2025-06-24 10:44:40.806  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 4 - START
2025-06-24 10:44:40.807  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {250, 130, 100x65} using slot 0 1.4759195119482E7 ms
2025-06-24 10:44:40.807  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 5 - START
2025-06-24 10:44:40.809  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {150, 195, 100x65} using slot 0 1.4759196557367E7 ms
2025-06-24 10:44:40.809  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 6 - START
2025-06-24 10:44:40.810  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {250, 195, 100x65} using slot 0 1.475919799979E7 ms
2025-06-24 10:44:40.810  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 7 - START
2025-06-24 10:44:40.816  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {150, 260, 100x65} using slot 0 1.4759200720944E7 ms
2025-06-24 10:44:40.816  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 8 - START
2025-06-24 10:44:40.822  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {250, 260, 100x65} using slot 0 1.4759205320597E7 ms
2025-06-24 10:44:40.822  2781-4990  NaApp                   com.dragon.clicker                   D  Storage: 9 - START
2025-06-24 10:44:40.822  2781-4990  NaApp                   com.dragon.clicker                   D  templatePaths.forEach matching {356, 30, 91x33} using slot 0 1.4759210573251E7 ms
2025-06-24 10:44:40.823  2781-4990  NaApp                   com.dragon.clicker                   D  Match: START extract quantity
2025-06-24 10:44:40.823  2781-4990  NaApp                   com.dragon.clicker                   D  Match: Start detectNumberFromScreen 91x33
2025-06-24 10:44:40.823  2781-4990  NaApp                   com.dragon.clicker                   D  Cache miss, loading: Img/SMG935F/dgis_delimiter_mask.png (scale: 1.0)
2025-06-24 10:44:40.824  2781-4990  NaApp                   com.dragon.clicker                   D  Template loaded using global resources: Img/SMG935F/dgis_delimiter_mask.png, size=13x20
2025-06-24 10:44:40.824  2781-4990  NaApp                   com.dragon.clicker                   D  Template no scaling needed: Img/SMG935F/dgis_delimiter_mask.png, size=13x20
2025-06-24 10:44:40.833  2781-4990  NaApp                   com.dragon.clicker                   D  Size: 79x14 | 14x79
2025-06-24 10:44:40.838  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 54..72, RangeY: 0..13
2025-06-24 10:44:40.842  2781-4990  NaApp                   com.dragon.clicker                   D  Cache miss, loading: Img/SMG935F/dgis_1.png (scale: 1.0)
2025-06-24 10:44:40.842  2781-4990  NaApp                   com.dragon.clicker                   D  Template loaded using global resources: Img/SMG935F/dgis_1.png, size=9x27
2025-06-24 10:44:40.843  2781-4990  NaApp                   com.dragon.clicker                   D  Template no scaling needed: Img/SMG935F/dgis_1.png, size=9x27
2025-06-24 10:44:40.844  2781-4990  NaApp                   com.dragon.clicker                   D  Size: 83x7 | 7x83
2025-06-24 10:44:40.844  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 0..8, RangeY: 0..6
2025-06-24 10:44:40.845  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 49..61, RangeY: 0..6
2025-06-24 10:44:40.846  2781-4990  NaApp                   com.dragon.clicker                   D  Cache miss, loading: Img/SMG935F/dgis_5.png (scale: 1.0)
2025-06-24 10:44:40.847  2781-4990  NaApp                   com.dragon.clicker                   D  Template loaded using global resources: Img/SMG935F/dgis_5.png, size=11x27
2025-06-24 10:44:40.847  2781-4990  NaApp                   com.dragon.clicker                   D  Template no scaling needed: Img/SMG935F/dgis_5.png, size=11x27
2025-06-24 10:44:40.848  2781-4990  NaApp                   com.dragon.clicker                   D  Size: 81x7 | 7x81
2025-06-24 10:44:40.848  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 4..20, RangeY: 0..6
2025-06-24 10:44:40.850  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 68..80, RangeY: 0..6
2025-06-24 10:44:40.851  2781-4990  NaApp                   com.dragon.clicker                   D  Cache miss, loading: Img/SMG935F/dgis_7.png (scale: 1.0)
2025-06-24 10:44:40.852  2781-4990  NaApp                   com.dragon.clicker                   D  Template loaded using global resources: Img/SMG935F/dgis_7.png, size=11x27
2025-06-24 10:44:40.852  2781-4990  NaApp                   com.dragon.clicker                   D  Template no scaling needed: Img/SMG935F/dgis_7.png, size=11x27
2025-06-24 10:44:40.857  2781-4990  NaApp                   com.dragon.clicker                   D  Size: 81x7 | 7x81
2025-06-24 10:44:40.858  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 57..73, RangeY: 0..6
2025-06-24 10:44:40.859  2781-4990  NaApp                   com.dragon.clicker                   D  Cache miss, loading: Img/SMG935F/dgis_8.png (scale: 1.0)
2025-06-24 10:44:40.860  2781-4990  NaApp                   com.dragon.clicker                   D  Template loaded using global resources: Img/SMG935F/dgis_8.png, size=15x27
2025-06-24 10:44:40.860  2781-4990  NaApp                   com.dragon.clicker                   D  Template no scaling needed: Img/SMG935F/dgis_8.png, size=15x27
2025-06-24 10:44:40.862  2781-4990  NaApp                   com.dragon.clicker                   D  Size: 77x7 | 7x77
2025-06-24 10:44:40.862  2781-4990  NaApp                   com.dragon.clicker                   D  RangeX: 12..34, RangeY: 0..6
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=1 at x=2, score=1.0
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=5 at x=12, score=0.9999999403953552
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=8 at x=23, score=1.000000238418579
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=1 at x=55, score=0.9990219473838806
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=99 at x=63, score=0.9796638488769531
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=7 at x=65, score=1.0
2025-06-24 10:44:40.865  2781-4990  NaApp                   com.dragon.clicker                   D  Match digit=5 at x=76, score=0.9953719973564148
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   W  Result: [DigitMatch(digit=1, point=Point(x=2, y=2), score=1.0), DigitMatch(digit=5, point=Point(x=12, y=1), score=0.9999999403953552), DigitMatch(digit=8, point=Point(x=23, y=2), score=1.000000238418579), DigitMatch(digit=1, point=Point(x=55, y=2), score=0.9990219473838806), DigitMatch(digit=99, point=Point(x=63, y=5), score=0.9796638488769531), DigitMatch(digit=7, point=Point(x=65, y=2), score=1.0), DigitMatch(digit=5, point=Point(x=76, y=1), score=0.9953719973564148)] | 1581/75
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   D  Number Found: 1581/75
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   W  Match: quantity null
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   W  Match: STORAGE quantity null
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   D  Global template Mat address: 531350700864
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   D  Active resource slots: 0/3
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   D  Template cache size: 5
2025-06-24 10:44:40.866  2781-4990  NaApp                   com.dragon.clicker                   D  compareListImage cleanup completed
2025-06-24 10:44:41.042  2781-4990  NaApp                   com.dragon.clicker                   D  Avail: 1704MB | Total: 3558MB
2025-06-24 10:44:41.042  2781-4990  NaApp                   com.dragon.clicker                   D  App Pss: 84 MB | Private Dirty: 69 MB
2025-06-24 10:44:41.042  2781-4990  NaApp                   com.dragon.clicker                   D  DONE DONE
