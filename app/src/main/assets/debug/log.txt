D  onStop activated
D  Hayday button clicked, isRunning=false
D  Hayday button clicked, isRunning=false
D  Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
D  Accessibility service enabled: true
D  Accessibility service enabled: true
D  OpencvService onStartCommand() called
D  OpencvService resultCode: -1, resultData: Intent { (has extras) }
D  OpencvService: Activity.RESULT_OK = -1
D  OpencvService: Creating MediaProjection
D  OpencvService: MediaProjection already exists: android.media.projection.MediaProjection@4c933c6
D  OpencvService: Setting up notification and starting foreground
D  OpencvService started successfully
D  Starting Hayday service...
D  Network available: true
D  Internet available: true
D  Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
D  Accessibility service enabled: true
D  OpencvService instance: true
D  Available memory before starting Hayday service: 1734 MB
D  Hayday service start requested
D  HaydayService initialized
D  Root access granted
D  getSourceBitmap: Starting controlled capture 640x360
D  Creating HandlerThread for hybrid approach
D  Creating ImageReader for hybrid approach
D  Creating persistent VirtualDisplay with improved filtering
D  Persistent VirtualDisplay created successfully with improved filtering
D  Setting up controlled capture callback
D  Triggering single screen capture
D  Requesting frame from VirtualDisplay surface
D  Processing expected ImageReader callback
D  Converting captured image to bitmap
W  ⚠️ Cannot get dimensions of recycled or null bitmap
W  ⚠️ Cannot get dimensions of recycled or null bitmap
D  Successfully created bitmap: 640x360
D  Controlled capture completed: true
D  Successfully captured bitmap using hybrid VirtualDisplay approach
D  Storage: 1 - START
D  templatePaths.forEach matching {150, 65, 100x65} using slot 0 1.0467077846475E7 ms
D  Storage: 2 - START
D  templatePaths.forEach matching {250, 65, 100x65} using slot 0 1.0467079510321E7 ms
D  Storage: 3 - START
D  templatePaths.forEach matching {150, 130, 100x65} using slot 0 1.0467080598629E7 ms
D  Storage: 4 - START
D  templatePaths.forEach matching {250, 130, 100x65} using slot 0 1.0467087771821E7 ms
D  Storage: 5 - START
D  templatePaths.forEach matching {150, 195, 100x65} using slot 0 1.0467092496552E7 ms
D  Storage: 6 - START
D  templatePaths.forEach matching {250, 195, 100x65} using slot 0 1.0467093896129E7 ms
D  Storage: 7 - START
D  templatePaths.forEach matching {150, 260, 100x65} using slot 0 1.0467095166013E7 ms
D  Storage: 8 - START
D  templatePaths.forEach matching {250, 260, 100x65} using slot 0 1.0467096554167E7 ms
D  Storage: 9 - START
D  templatePaths.forEach matching {358, 27, 86x24} using slot 0 1.0467097780937E7 ms
D  Match: START extract quantity
D  Match: Start detectNumberFromScreen 86x24
D  Cache miss, loading: Img/SMG935F/dgis_delimiter_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_delimiter_mask.png, size=13x20
D  Template no scaling needed: Img/SMG935F/dgis_delimiter_mask.png, size=13x20
D  Cache hit: Img/SMG935F/dgis_delimiter_mask.png (scale: 1.0)
D  Size: 74x5 | 5x74
D  RangeX: 50..68, RangeY: 0..4
D  RangeX: 32..50, RangeY: 0..4
D  Cache miss, loading: Img/SMG935F/dgis_1.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_1.png, size=10x20
D  Template no scaling needed: Img/SMG935F/dgis_1.png, size=10x20
D  Cache miss, loading: Img/SMG935F/dgis_1_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_1_mask.png, size=10x20
D  Template no scaling needed: Img/SMG935F/dgis_1_mask.png, size=10x20
D  Size: 77x5 | 5x77
D  RangeX: 13..27, RangeY: 0..4
D  Cache miss, loading: Img/SMG935F/dgis_2.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_2.png, size=12x19
D  Template no scaling needed: Img/SMG935F/dgis_2.png, size=12x19
D  Cache miss, loading: Img/SMG935F/dgis_2_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_2_mask.png, size=12x19
D  Template no scaling needed: Img/SMG935F/dgis_2_mask.png, size=12x19
D  Size: 75x6 | 6x75
D  RangeX: 17..35, RangeY: 0..5
D  Cache miss, loading: Img/SMG935F/dgis_5.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_5.png, size=10x17
D  Template no scaling needed: Img/SMG935F/dgis_5.png, size=10x17
D  Cache miss, loading: Img/SMG935F/dgis_5_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_5_mask.png, size=10x17
D  Template no scaling needed: Img/SMG935F/dgis_5_mask.png, size=10x17
D  Size: 77x8 | 8x77
D  RangeX: 27..41, RangeY: 0..7
D  Cache miss, loading: Img/SMG935F/dgis_6.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_6.png, size=15x22
D  Template no scaling needed: Img/SMG935F/dgis_6.png, size=15x22
D  Cache miss, loading: Img/SMG935F/dgis_6_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_6_mask.png, size=15x22
D  Template no scaling needed: Img/SMG935F/dgis_6_mask.png, size=15x22
D  Size: 72x3 | 3x72
D  RangeX: 0..12, RangeY: 0..2
D  Cache miss, loading: Img/SMG935F/dgis_7.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_7.png, size=9x16
D  Template no scaling needed: Img/SMG935F/dgis_7.png, size=9x16
D  Cache miss, loading: Img/SMG935F/dgis_7_mask.png (scale: 1.0)
D  Template loaded using global resources: Img/SMG935F/dgis_7_mask.png, size=9x16
D  Template no scaling needed: Img/SMG935F/dgis_7_mask.png, size=9x16
D  Size: 78x9 | 9x78
D  RangeX: 21..33, RangeY: 0..8
D  Match digit=6 at x=1, score=1.0
D  Match digit=1 at x=20, score=1.0
D  Match digit=2 at x=26, score=1.0000001192092896
D  Match digit=7 at x=27, score=1.0000001192092896
D  Match digit=5 at x=34, score=0.9999998211860657
D  Match digit=99 at x=41, score=0.976076066493988
D  Match digit=99 at x=59, score=0.9784857034683228
W  Result: [DigitMatch(digit=6, point=Point(x=1, y=2), score=1.0), DigitMatch(digit=1, point=Point(x=20, y=3), score=1.0), DigitMatch(digit=2, point=Point(x=26, y=4), score=1.0000001192092896), DigitMatch(digit=7, point=Point(x=27, y=5), score=1.0000001192092896), DigitMatch(digit=5, point=Point(x=34, y=4), score=0.9999998211860657), DigitMatch(digit=99, point=Point(x=41, y=1), score=0.976076066493988), DigitMatch(digit=99, point=Point(x=59, y=0), score=0.9784857034683228)] | 61275//
D  Number Found: 61275//
W  Match: quantity null
W  Match: STORAGE quantity null
D  Global template Mat address: 531350700864
D  Active resource slots: 0/3
D  Template cache size: 11
D  compareListImage cleanup completed
D  Avail: 1722MB | Total: 3558MB
D  App Pss: 83 MB | Private Dirty: 68 MB
D  DONE DONE
