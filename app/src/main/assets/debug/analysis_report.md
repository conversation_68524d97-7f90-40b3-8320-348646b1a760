# Phân tích vấn đề Template Matching và Number Detection

## Vấn đề được phát hiện:

### 1. Từ log.txt:
- <PERSON><PERSON> thống detect được số "61275//" từ hình ảnh
- Nhưng kết quả cuối cùng là `quantity null`
- <PERSON><PERSON> cho thấy: "Number Found: 61275//" nhưng "Match: quantity null"

### 2. Nguyên nhân chính:
- Hàm `extractQuantityFromImage()` trong OpencvService.kt luôn return `null`
- Dòng 2007 (cũ): `return null` - không xử lý kết quả từ `detectNumberFromScreen()`

### 3. Chi tiết kỹ thuật:
- `detectNumberFromScreen()` hoạt động đúng, trả về string "61275//"
- Digit 99 được map thành "/" (delimiter)
- Template matching với mask hoạt động tốt
- Vấn đề chỉ ở việc parse string thành integer

## Gi<PERSON>i pháp đã áp dụng:

### 1. <PERSON><PERSON><PERSON> hàm `extractQuantityFromImage()`:
```kotlin
// Cũ:
return null

// Mới:
if (number.isNotEmpty()) {
    try {
        val cleanNumber = when {
            number.contains("/") -> number.split("/")[0]
            else -> number
        }
        
        if (cleanNumber.isNotEmpty() && cleanNumber.all { it.isDigit() }) {
            val quantity = cleanNumber.toInt()
            Log.d(ApiConstant.LOG_TAG, "Match: quantity parsed successfully: $quantity (from: $number)")
            return quantity
        }
    } catch (e: NumberFormatException) {
        Log.e(ApiConstant.LOG_TAG, "Match: Failed to parse quantity: $number", e)
    }
}
return null
```

### 2. Enable debug images:
- Uncomment các dòng saveMatAsImage() để lưu hình debug
- Lưu Screen, Template, và Mask images với tên có digit identifier
- Giảm delay từ 2000ms xuống 100ms để tăng tốc debug

## Kết quả mong đợi:

### Trước khi sửa:
- Log: "Number Found: 61275//"
- Log: "Match: quantity null"
- Return: null

### Sau khi sửa:
- Log: "Number Found: 61275//"
- Log: "Match: quantity parsed successfully: 61275 (from: 61275//)"
- Return: 61275

## Các trường hợp được handle:

1. **"61275//"** → 61275 (bỏ delimiter)
2. **"61275/"** → 61275 (bỏ delimiter)
3. **"61275"** → 61275 (không có delimiter)
4. **""** → null (empty string)
5. **"abc123"** → null (có ký tự không phải số)

## Ghi chú:

- Template matching sử dụng DGIS (digit) templates với mask
- Threshold = 0.97 cho độ chính xác cao
- Chỉ enable một số digit templates: 1, 2, 5, 6, 7, 99 (delimiter)
- Có thể cần enable thêm digit 0, 3, 4, 8, 9 nếu cần thiết

## Test case:

Để test, chạy `getInformationStorage()` và kiểm tra log:
- Tìm "Number Found:" để xem string được detect
- Tìm "Match: quantity parsed successfully:" để xem số được parse
- Kiểm tra debug images trong thư mục Dragon/Img
