06-12 22:58:25.875 D/NaApp   ( 4572): Command success: am force-stop com.supercell.hayday
06-12 22:58:26.011 D/NaApp   ( 4572): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 22:58:26.063 D/NaApp   ( 4572): Command success: service call SurfaceFlinger 1008 i32 0
06-12 22:58:26.139 D/NaApp   ( 4572): Command success: sync
06-12 22:58:26.398 D/NaApp   ( 4572): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 22:58:26.669 D/NaApp   ( 4572): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 22:58:26.722 D/NaApp   ( 4572): Command success: pm trim-caches 100M
06-12 22:58:26.778 D/NaApp   ( 4572): Command success: am kill-all
06-12 22:58:26.778 D/NaApp   ( 4572): Killed com.supercell.hayday
06-12 22:58:27.714 D/NaApp   ( 4572): Hayday button clicked, isRunning=true
06-12 22:58:27.714 D/NaApp   ( 4572): Hayday button clicked, isRunning=true
06-12 22:58:27.716 D/NaApp   ( 4572): Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
06-12 22:58:27.716 D/NaApp   ( 4572): Accessibility service enabled: true
06-12 22:58:27.717 D/NaApp   ( 4572): Accessibility service enabled: true
06-12 22:58:27.816 E/NaApp   ( 4572): Error in tutorial
06-12 22:58:27.816 E/NaApp   ( 4572): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@4ad6d3a
06-12 22:58:27.817 E/NaApp   ( 4572): Exception in HaydayService
06-12 22:58:27.817 E/NaApp   ( 4572): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@4ad6d3a
06-12 22:58:27.818 E/NaApp   ( 4572): Automation error
06-12 22:58:27.818 E/NaApp   ( 4572): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@4ad6d3a
06-12 23:00:34.331 D/NaApp   (10049): OpenCV loaded successfully! Version: 4.11.0
06-12 23:00:34.373 D/NaApp   (10049): Requesting battery optimization exclusion...
06-12 23:00:34.410 D/NaApp   (10049): OpenCV loaded successfully! Version: 4.11.0
06-12 23:00:34.412 D/NaApp   (10049): Requesting overlay permission...
06-12 23:01:27.061 D/NaApp   (10049): Controlled capture completed: true
06-12 23:01:27.063 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:01:27.065 D/NaApp   (10049): grabScreenshot completed in 78.716654 ms
06-12 23:01:27.168 D/NaApp   (10049): Avail: 2124MB | Total: 3558MB
06-12 23:01:27.168 D/NaApp   (10049): App Pss: 63 MB | Private Dirty: 33 MB
06-12 23:01:27.168 D/NaApp   (10049): templatePaths.forEach started at 4468.759843 ms
06-12 23:01:27.168 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:01:27.168 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4468.903882 ms
06-12 23:01:27.168 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087168
06-12 23:01:27.168 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.168 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.168 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087168
06-12 23:01:27.168 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.168 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.168 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.169 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4469.577382 ms
06-12 23:01:27.169 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:01:27.169 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.169 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.169 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:01:27.170 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.26569831371307373, threshold=0.9, point=(44.0, 314.0)
06-12 23:01:27.170 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.26569831371307373 < threshold=0.9
06-12 23:01:27.170 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4471.107959 ms
06-12 23:01:27.222 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744087221
06-12 23:01:27.222 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:27.222 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744087221
06-12 23:01:27.222 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:27.222 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:27.222 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.222 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4522.795382 ms
06-12 23:01:27.222 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087222
06-12 23:01:27.222 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.222 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.222 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087222
06-12 23:01:27.222 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.222 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.222 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.222 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4523.438228 ms
06-12 23:01:27.223 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:01:27.223 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.223 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.223 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:01:27.224 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.2053816020488739, threshold=0.9, point=(43.0, 314.0)
06-12 23:01:27.224 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.2053816020488739 >= threshold=0.9? false, brightness=248.87276785714283 < 100? false
06-12 23:01:27.224 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4524.899189 ms
06-12 23:01:27.282 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744087282
06-12 23:01:27.282 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:27.282 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744087282
06-12 23:01:27.282 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:27.282 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:27.282 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.282 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:01:27.282 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:01:27.283 D/NaApp   (10049): Template cache size: 27
06-12 23:01:27.283 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:01:27.369 D/NaApp   (10049): Avail: 2090MB | Total: 3558MB
06-12 23:01:27.369 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:01:27.369 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:01:27.369 D/NaApp   (10049): compareListImage started iteration 11 at 4669.776535 ms
06-12 23:01:27.422 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:01:27.423 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:01:27.423 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:01:27.426 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:01:27.426 D/NaApp   (10049): Triggering single screen capture
06-12 23:01:27.426 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:01:27.438 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:01:27.438 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:01:27.440 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:01:27.448 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:01:27.448 D/NaApp   (10049): Controlled capture completed: true
06-12 23:01:27.451 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:01:27.461 D/NaApp   (10049): grabScreenshot completed in 91.792654 ms
06-12 23:01:27.555 D/NaApp   (10049): Avail: 2056MB | Total: 3558MB
06-12 23:01:27.555 D/NaApp   (10049): App Pss: 63 MB | Private Dirty: 33 MB
06-12 23:01:27.555 D/NaApp   (10049): templatePaths.forEach started at 4855.695305 ms
06-12 23:01:27.555 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:01:27.555 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4855.806728 ms
06-12 23:01:27.555 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087555
06-12 23:01:27.555 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.555 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.555 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087555
06-12 23:01:27.555 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.555 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.555 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.556 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4856.508266 ms
06-12 23:01:27.556 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:01:27.556 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:01:27.559 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:01:27.559 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:01:27.559 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:01:27.559 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.559 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.560 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:01:27.560 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.2691906988620758, threshold=0.9, point=(44.0, 314.0)
06-12 23:01:27.560 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.2691906988620758 < threshold=0.9
06-12 23:01:27.560 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4861.393035 ms
06-12 23:01:27.612 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744087611
06-12 23:01:27.612 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:27.612 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744087611
06-12 23:01:27.612 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:27.612 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:27.612 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.612 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4912.755881 ms
06-12 23:01:27.612 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087612
06-12 23:01:27.612 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.612 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.612 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087612
06-12 23:01:27.612 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.612 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.612 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.613 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4913.454266 ms
06-12 23:01:27.613 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:01:27.613 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:01:27.615 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:01:27.615 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:01:27.616 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:01:27.616 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.616 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.616 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:01:27.617 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.2314562052488327, threshold=0.9, point=(43.0, 314.0)
06-12 23:01:27.617 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.2314562052488327 >= threshold=0.9? false, brightness=247.5111607142857 < 100? false
06-12 23:01:27.617 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4917.894997 ms
06-12 23:01:27.668 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744087668
06-12 23:01:27.668 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:27.668 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744087668
06-12 23:01:27.668 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:27.668 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:27.668 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.668 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:01:27.668 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:01:27.668 D/NaApp   (10049): Template cache size: 27
06-12 23:01:27.668 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:01:27.749 D/NaApp   (10049): Avail: 2009MB | Total: 3558MB
06-12 23:01:27.749 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:01:27.749 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:01:27.749 D/NaApp   (10049): compareListImage started iteration 12 at 5049.888304 ms
06-12 23:01:27.800 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:01:27.800 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:01:27.800 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:01:27.800 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:01:27.800 D/NaApp   (10049): Triggering single screen capture
06-12 23:01:27.800 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:01:27.804 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:01:27.804 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:01:27.806 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:01:27.811 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:01:27.811 D/NaApp   (10049): Controlled capture completed: true
06-12 23:01:27.812 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:01:27.813 D/NaApp   (10049): grabScreenshot completed in 63.872308 ms
06-12 23:01:27.928 D/NaApp   (10049): Avail: 2008MB | Total: 3558MB
06-12 23:01:27.928 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:01:27.928 D/NaApp   (10049): templatePaths.forEach started at 5229.102112 ms
06-12 23:01:27.928 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:01:27.928 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5229.249612 ms
06-12 23:01:27.929 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087928
06-12 23:01:27.929 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.929 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.929 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087928
06-12 23:01:27.929 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.929 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.929 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.929 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5230.036766 ms
06-12 23:01:27.929 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:01:27.929 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.930 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.930 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:01:27.931 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.30887269973754883, threshold=0.9, point=(44.0, 314.0)
06-12 23:01:27.931 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.30887269973754883 < threshold=0.9
06-12 23:01:27.931 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5231.964958 ms
06-12 23:01:27.982 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744087982
06-12 23:01:27.982 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:27.982 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744087982
06-12 23:01:27.983 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:27.983 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:27.983 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.983 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5283.634843 ms
06-12 23:01:27.983 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744087983
06-12 23:01:27.983 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:27.983 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:27.983 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744087983
06-12 23:01:27.983 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:27.983 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:27.983 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:27.984 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5284.621189 ms
06-12 23:01:27.984 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:01:27.984 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:27.984 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:27.985 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:01:27.986 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.2846386730670929, threshold=0.9, point=(43.0, 312.0)
06-12 23:01:27.986 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.2846386730670929 >= threshold=0.9? false, brightness=246.13839285714283 < 100? false
06-12 23:01:27.986 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5287.065112 ms
06-12 23:01:28.041 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744088038
06-12 23:01:28.041 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:28.041 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744088038
06-12 23:01:28.041 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:28.041 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:28.041 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:28.041 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:01:28.041 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:01:28.044 D/NaApp   (10049): Template cache size: 27
06-12 23:01:28.044 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:01:28.167 D/NaApp   (10049): Avail: 2008MB | Total: 3558MB
06-12 23:01:28.167 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:01:28.167 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:01:28.167 D/NaApp   (10049): compareListImage started iteration 13 at 5467.66265 ms
06-12 23:01:28.218 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:01:28.219 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:01:28.219 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:01:28.220 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:01:28.220 D/NaApp   (10049): Triggering single screen capture
06-12 23:01:28.220 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:01:28.222 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:01:28.225 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:01:28.230 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:01:28.247 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:01:28.247 D/NaApp   (10049): Controlled capture completed: true
06-12 23:01:28.250 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:01:28.257 D/NaApp   (10049): grabScreenshot completed in 89.925923 ms
06-12 23:01:28.375 D/NaApp   (10049): Avail: 2011MB | Total: 3558MB
06-12 23:01:28.375 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:01:28.375 D/NaApp   (10049): templatePaths.forEach started at 5675.907304 ms
06-12 23:01:28.375 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:01:28.375 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5676.043919 ms
06-12 23:01:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744088375
06-12 23:01:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:28.375 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744088375
06-12 23:01:28.375 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:28.376 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5676.922919 ms
06-12 23:01:28.376 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:01:28.376 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:01:28.380 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:01:28.380 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:01:28.381 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:01:28.381 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:28.381 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:28.381 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:01:28.382 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.35963428020477295, threshold=0.9, point=(20.0, 324.0)
06-12 23:01:28.382 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.35963428020477295 < threshold=0.9
06-12 23:01:28.382 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5683.369266 ms
06-12 23:01:28.433 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744088433
06-12 23:01:28.433 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:28.434 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744088433
06-12 23:01:28.434 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:28.434 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:28.434 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:28.434 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5734.596958 ms
06-12 23:01:28.434 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744088434
06-12 23:01:28.434 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:01:28.434 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:01:28.434 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744088434
06-12 23:01:28.434 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:01:28.434 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:01:28.434 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:28.434 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5735.301035 ms
06-12 23:01:28.434 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:01:28.434 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:01:28.437 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:01:28.438 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:01:28.438 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:01:28.438 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:01:28.438 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:01:28.438 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:01:28.439 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.36694100499153137, threshold=0.9, point=(19.0, 324.0)
06-12 23:01:28.439 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.36694100499153137 >= threshold=0.9? false, brightness=244.66964285714283 < 100? false
06-12 23:01:28.439 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5740.365035 ms
06-12 23:01:28.491 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744088490
06-12 23:01:28.491 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:01:28.491 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744088490
06-12 23:01:28.491 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:01:28.491 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:01:28.491 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:01:28.491 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:01:28.491 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:01:28.491 D/NaApp   (10049): Template cache size: 27
06-12 23:01:28.491 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:01:28.491 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:01:28.492 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:01:28.492 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:01:28.492 D/NaApp   (10049): Template cache size: 27
06-12 23:01:28.492 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:01:28.492 D/NaApp   (10049): compareImagesLong result at 5792.84265 ms: FAILED
06-12 23:01:28.492 D/NaApp   (10049): checkLoadGame: compareImagesLong completed in 5793.199265 ms
06-12 23:01:28.492 D/NaApp   (10049): Check load game: (FAILED, null)
06-12 23:01:28.492 E/NaApp   (10049): Check failed: (FAILED, null)
06-12 23:01:28.493 E/NaApp   (10049): Fail Farm 2
06-12 23:01:28.494 D/NaApp   (10049): Automation cycle completed
06-12 23:01:28.679 D/NaApp   (10049): Auto index: 1
06-12 23:01:28.723 D/NaApp   (10049): Avail: 2009MB | Total: 3558MB
06-12 23:01:28.723 D/NaApp   (10049): App Pss: 63 MB | Private Dirty: 33 MB
06-12 23:01:29.084 D/NaApp   (10049): CPU Usage: 43%
06-12 23:01:29.087 D/NaApp   (10049): Folder already exists: /storage/emulated/0/Dragon/Farm
06-12 23:01:29.089 W/NaApp   (10049): Go Home
06-12 23:01:30.127 D/NaApp   (10049): Command success: am force-stop com.supercell.hayday
06-12 23:01:30.207 D/NaApp   (10049): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:01:30.271 D/NaApp   (10049): Command success: service call SurfaceFlinger 1008 i32 0
06-12 23:01:30.394 D/NaApp   (10049): Command success: sync
06-12 23:01:30.710 D/NaApp   (10049): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 23:01:30.931 D/NaApp   (10049): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 23:01:30.993 D/NaApp   (10049): Command success: pm trim-caches 100M
06-12 23:01:31.091 D/NaApp   (10049): Command success: am kill-all
06-12 23:01:31.091 D/NaApp   (10049): Killed com.supercell.hayday
06-12 23:01:31.771 D/NaApp   (10049): Hayday button clicked, isRunning=true
06-12 23:01:31.771 D/NaApp   (10049): Hayday button clicked, isRunning=true
06-12 23:01:31.774 D/NaApp   (10049): Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
06-12 23:01:31.774 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:01:31.774 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:01:31.898 E/NaApp   (10049): Error in tutorial
06-12 23:01:31.898 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@9f8b2be
06-12 23:01:31.900 E/NaApp   (10049): Exception in HaydayService
06-12 23:01:31.900 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@9f8b2be
06-12 23:01:31.902 E/NaApp   (10049): Automation error
06-12 23:01:31.902 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@9f8b2be
06-12 23:07:36.419 D/NaApp   (10049): Hayday button clicked, isRunning=false
06-12 23:07:36.419 D/NaApp   (10049): Hayday button clicked, isRunning=false
06-12 23:07:36.425 D/NaApp   (10049): Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
06-12 23:07:36.425 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:07:36.426 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:07:36.426 D/NaApp   (10049): Starting Hayday service...
06-12 23:07:36.427 D/NaApp   (10049): Network available: true
06-12 23:07:36.430 D/NaApp   (10049): Internet available: true
06-12 23:07:36.432 D/NaApp   (10049): Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
06-12 23:07:36.432 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:07:36.432 D/NaApp   (10049): OpencvService instance: true
06-12 23:07:36.435 D/NaApp   (10049): Available memory before starting Hayday service: 2633 MB
06-12 23:07:36.445 D/NaApp   (10049): Hayday service start requested
06-12 23:07:36.502 D/NaApp   (10049): HaydayService initialized
06-12 23:07:36.502 D/NaApp   (10049): Root access granted
06-12 23:07:36.505 D/NaApp   (10049): Starting killAll process...
06-12 23:07:36.563 D/NaApp   (10049): Disabled com.android.bluetooth
06-12 23:07:36.620 D/NaApp   (10049): Disabled com.android.nfc
06-12 23:07:36.684 D/NaApp   (10049): Disabled com.android.vending
06-12 23:07:36.727 D/NaApp   (10049): Disabled com.google.android.gms
06-12 23:07:36.780 D/NaApp   (10049): Disabled com.google.android.gsf
06-12 23:07:36.828 D/NaApp   (10049): Disabled com.google.android.tts
06-12 23:07:36.877 D/NaApp   (10049): Disabled com.google.android.syncadapters.calendar
06-12 23:07:36.919 D/NaApp   (10049): Disabled com.google.android.partnersetup
06-12 23:07:36.966 D/NaApp   (10049): Disabled com.google.android.apps.docs
06-12 23:07:37.007 D/NaApp   (10049): Disabled com.android.email
06-12 23:07:37.050 D/NaApp   (10049): Disabled org.lineageos.updater
06-12 23:07:37.091 D/NaApp   (10049): Disabled mid.oed.wedp
06-12 23:07:37.139 D/NaApp   (10049): Disabled com.vng.inputmethod.labankey
06-12 23:07:37.181 D/NaApp   (10049): Disabled com.android.inputmethod.latin
06-12 23:07:37.209 D/NaApp   (10049): Denied background for mid.oed.wedp
06-12 23:07:37.238 D/NaApp   (10049): Denied background for com.google.android.tts
06-12 23:07:37.268 D/NaApp   (10049): Denied background for org.lineageos.updater
06-12 23:07:37.299 D/NaApp   (10049): Denied background for com.google.android.gms
06-12 23:07:37.327 D/NaApp   (10049): Denied background for com.google.android.gsf
06-12 23:07:37.390 D/NaApp   (10049): CMD is sucess: am kill-all
06-12 23:07:37.736 D/NaApp   (10049): CMD is sucess: pm trim-caches 999999M
06-12 23:07:37.775 D/NaApp   (10049): CMD is sucess: settings put system screen_brightness 128
06-12 23:07:37.823 D/NaApp   (10049): CMD is sucess: am startservice -n com.android.systemui/.SystemUIService
06-12 23:07:37.854 D/NaApp   (10049): CMD is sucess: rm -rf /data/data/com.dragon.clicker/cache/*
06-12 23:07:37.882 D/NaApp   (10049): CMD is sucess: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:07:37.908 D/NaApp   (10049): CMD is sucess: rm -rf /sdcard/Dragon/temp/*
06-12 23:07:37.944 D/NaApp   (10049): CMD is sucess: sync
06-12 23:07:38.008 D/NaApp   (10049): CMD is sucess: settings put system haptic_feedback_enabled 0
06-12 23:07:38.064 D/NaApp   (10049): CMD is sucess: settings put secure touch_vibration_enabled 0
06-12 23:07:38.114 D/NaApp   (10049): CMD is sucess: settings put system sound_effects_enabled 0
06-12 23:07:38.180 D/NaApp   (10049): CMD is sucess: settings put global zen_mode 2
06-12 23:07:38.214 D/NaApp   (10049): CMD is sucess: dumpsys battery set level 9999
06-12 23:07:38.259 D/NaApp   (10049): CMD is sucess: settings put global low_power_trigger_level 0
06-12 23:07:38.301 D/NaApp   (10049): CMD is sucess: settings put global low_battery_sound 0
06-12 23:07:38.341 D/NaApp   (10049): CMD is sucess: settings put global power_sounds_enabled 0
06-12 23:07:38.344 D/NaApp   (10049): Updated scale for Img/SMG935F/shopfirst.png: 1.0 (size: 46.0x42.0)
06-12 23:07:38.345 D/NaApp   (10049): Updated scale for Img/SMG935F/loading_shop.png: 1.0 (size: 51.0x46.0)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/start_newfarm.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/buy_chicken.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/buy_chicken_done.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/keyboard_hayday.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/coop.png (scale: 0.33)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/put_name.png (scale: 0.25)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/free_eggs.png (scale: 0.5)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/done_free_eggs.png (scale: 0.5)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/free_wheat.png (scale: 0.5)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/levelup.png (scale: 0.25)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/level1.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/level2.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/level3.png (scale: 0.75)
06-12 23:07:38.346 D/NaApp   (10049): Cache hit: Img/SMG935F/level4.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/level5.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/level6.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/level7.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC_Done.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/truck_task.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/truck_task.png (scale: 0.75)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/check_field.png (scale: 0.5)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/shopfirst.png (scale: 1.0)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/loading_shop.png (scale: 1.0)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/newfarm6.png (scale: 0.33)
06-12 23:07:38.347 D/NaApp   (10049): Cache hit: Img/SMG935F/done_free_wheat1.png (scale: 0.5)
06-12 23:07:38.460 D/NaApp   (10049): Avail: 2651MB | Total: 3558MB
06-12 23:07:38.460 D/NaApp   (10049): App Pss: 67 MB | Private Dirty: 33 MB
06-12 23:07:38.823 D/NaApp   (10049): CPU Usage: 35%
06-12 23:07:38.840 D/NaApp   (10049): Folder already exists: /storage/emulated/0/Dragon/Farm
06-12 23:07:38.857 W/NaApp   (10049): Go Home
06-12 23:07:39.562 D/NaApp   (10049): Command success: am force-stop com.supercell.hayday
06-12 23:07:39.648 D/NaApp   (10049): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:07:39.724 D/NaApp   (10049): Command success: service call SurfaceFlinger 1008 i32 0
06-12 23:07:39.788 D/NaApp   (10049): Command success: sync
06-12 23:07:39.992 D/NaApp   (10049): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 23:07:40.137 D/NaApp   (10049): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 23:07:40.230 D/NaApp   (10049): Command success: pm trim-caches 100M
06-12 23:07:40.295 D/NaApp   (10049): Command success: am kill-all
06-12 23:07:40.295 D/NaApp   (10049): Killed com.supercell.hayday
06-12 23:07:41.706 D/NaApp   (10049): Successfully applied Farm #1
06-12 23:07:50.288 D/NaApp   (10049): Open Farm: 1 / 10
06-12 23:07:50.295 D/NaApp   (10049): Farm opened successfully
06-12 23:07:50.296 D/NaApp   (10049): Checking game load
06-12 23:07:50.296 D/NaApp   (10049): checkLoadGame: compareImagesLong started at 932613.979794 ms
06-12 23:07:50.386 D/NaApp   (10049): Avail: 2319MB | Total: 3558MB
06-12 23:07:50.386 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:07:50.387 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:50.387 D/NaApp   (10049): compareListImage started iteration 0 at 90.952039 ms
06-12 23:07:50.437 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:50.438 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:50.438 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:50.438 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:50.438 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:50.438 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:50.445 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:50.446 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:50.454 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:50.465 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:50.465 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:50.466 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:50.467 D/NaApp   (10049): grabScreenshot completed in 80.2855 ms
06-12 23:07:50.677 D/NaApp   (10049): Avail: 2310MB | Total: 3558MB
06-12 23:07:50.677 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:50.677 D/NaApp   (10049): templatePaths.forEach started at 381.8895 ms
06-12 23:07:50.678 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:50.678 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 381.966115 ms
06-12 23:07:50.678 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744470678
06-12 23:07:50.678 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:50.678 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:50.678 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744470678
06-12 23:07:50.678 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:50.678 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:50.678 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:50.678 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 382.797846 ms
06-12 23:07:50.678 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:50.679 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:50.679 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:50.679 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:50.679 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.18370847404003143, threshold=0.9, point=(22.0, 324.0)
06-12 23:07:50.679 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.18370847404003143 < threshold=0.9
06-12 23:07:50.679 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 383.864385 ms
06-12 23:07:50.733 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744470732
06-12 23:07:50.733 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:50.733 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744470732
06-12 23:07:50.733 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:50.733 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:50.733 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:50.733 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 437.159231 ms
06-12 23:07:50.733 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744470733
06-12 23:07:50.733 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:50.733 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:50.733 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744470733
06-12 23:07:50.733 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:50.733 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:50.733 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:50.733 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 437.645346 ms
06-12 23:07:50.733 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:50.733 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:50.734 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:50.734 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:50.734 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.20122575759887695, threshold=0.9, point=(23.0, 324.0)
06-12 23:07:50.734 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.20122575759887695 >= threshold=0.9? false, brightness=245.71540178571428 < 100? false
06-12 23:07:50.734 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 438.705961 ms
06-12 23:07:50.785 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744470785
06-12 23:07:50.785 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:50.785 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744470785
06-12 23:07:50.785 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:50.785 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:50.785 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:50.785 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:50.785 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:50.785 D/NaApp   (10049): Template cache size: 27
06-12 23:07:50.785 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:50.890 D/NaApp   (10049): Avail: 2279MB | Total: 3558MB
06-12 23:07:50.890 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:50.890 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:50.890 D/NaApp   (10049): compareListImage started iteration 1 at 594.627154 ms
06-12 23:07:50.941 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:50.941 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:50.941 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:50.944 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:50.944 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:50.944 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:50.957 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:50.957 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:50.959 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:50.964 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:50.964 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:50.970 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:50.970 D/NaApp   (10049): grabScreenshot completed in 79.911269 ms
06-12 23:07:51.020 D/NaApp   (10049): Avail: 2240MB | Total: 3558MB
06-12 23:07:51.020 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:07:51.020 D/NaApp   (10049): templatePaths.forEach started at 724.029 ms
06-12 23:07:51.020 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:51.020 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 724.111577 ms
06-12 23:07:51.020 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471020
06-12 23:07:51.020 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.020 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.020 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471020
06-12 23:07:51.020 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.020 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.020 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.020 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 724.542654 ms
06-12 23:07:51.020 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:07:51.020 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:51.024 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:07:51.024 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:07:51.025 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:07:51.025 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.025 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.025 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:51.025 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.18330833315849304, threshold=0.9, point=(24.0, 324.0)
06-12 23:07:51.025 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.18330833315849304 < threshold=0.9
06-12 23:07:51.025 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 729.838423 ms
06-12 23:07:51.076 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471076
06-12 23:07:51.076 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.076 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471076
06-12 23:07:51.076 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.076 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.076 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.076 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 780.764846 ms
06-12 23:07:51.076 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471076
06-12 23:07:51.076 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.076 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.076 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471076
06-12 23:07:51.076 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.076 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.076 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.077 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 781.15223 ms
06-12 23:07:51.077 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:07:51.077 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:51.079 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:07:51.080 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:07:51.081 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:07:51.081 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.081 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.081 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:51.081 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.20062051713466644, threshold=0.9, point=(25.0, 324.0)
06-12 23:07:51.081 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.20062051713466644 >= threshold=0.9? false, brightness=245.6015625 < 100? false
06-12 23:07:51.081 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 785.886307 ms
06-12 23:07:51.132 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471132
06-12 23:07:51.132 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.132 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471132
06-12 23:07:51.132 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.132 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.132 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.132 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:51.132 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:51.132 D/NaApp   (10049): Template cache size: 27
06-12 23:07:51.132 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:51.214 D/NaApp   (10049): Avail: 2231MB | Total: 3558MB
06-12 23:07:51.214 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:51.214 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:51.214 D/NaApp   (10049): compareListImage started iteration 2 at 918.119346 ms
06-12 23:07:51.264 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:51.265 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:51.265 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:51.265 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:51.265 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:51.265 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:51.267 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:51.267 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:51.279 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:51.291 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:51.291 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:51.295 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:51.296 D/NaApp   (10049): grabScreenshot completed in 81.889039 ms
06-12 23:07:51.425 D/NaApp   (10049): Avail: 2238MB | Total: 3558MB
06-12 23:07:51.425 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:51.426 D/NaApp   (10049): templatePaths.forEach started at 1129.918846 ms
06-12 23:07:51.426 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:51.426 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1129.989615 ms
06-12 23:07:51.426 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471426
06-12 23:07:51.426 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.426 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.426 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471426
06-12 23:07:51.426 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.426 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.426 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.426 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1130.492115 ms
06-12 23:07:51.426 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:51.426 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.426 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.427 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:51.427 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.1810052990913391, threshold=0.9, point=(32.0, 324.0)
06-12 23:07:51.427 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.1810052990913391 < threshold=0.9
06-12 23:07:51.427 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1131.530922 ms
06-12 23:07:51.479 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471479
06-12 23:07:51.479 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.479 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471479
06-12 23:07:51.479 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.479 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.479 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.479 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1183.644192 ms
06-12 23:07:51.479 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471479
06-12 23:07:51.479 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.479 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.479 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471479
06-12 23:07:51.479 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.479 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.479 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.480 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1184.129692 ms
06-12 23:07:51.480 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:51.480 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.480 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.480 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:51.481 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.19789279997348785, threshold=0.9, point=(31.0, 324.0)
06-12 23:07:51.481 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.19789279997348785 >= threshold=0.9? false, brightness=245.24441964285714 < 100? false
06-12 23:07:51.481 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1185.805115 ms
06-12 23:07:51.532 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471532
06-12 23:07:51.532 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.533 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471532
06-12 23:07:51.533 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.533 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.533 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.533 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:51.533 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:51.533 D/NaApp   (10049): Template cache size: 27
06-12 23:07:51.533 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:51.643 D/NaApp   (10049): Avail: 2229MB | Total: 3558MB
06-12 23:07:51.643 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:51.643 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:51.643 D/NaApp   (10049): compareListImage started iteration 3 at 1347.185653 ms
06-12 23:07:51.694 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:51.694 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:51.694 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:51.695 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:51.696 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:51.696 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:51.707 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:51.708 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:51.710 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:51.713 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:51.713 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:51.714 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:51.715 D/NaApp   (10049): grabScreenshot completed in 72.047961 ms
06-12 23:07:51.781 D/NaApp   (10049): Avail: 2211MB | Total: 3558MB
06-12 23:07:51.781 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:51.781 D/NaApp   (10049): templatePaths.forEach started at 1485.68823 ms
06-12 23:07:51.781 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:51.781 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1485.763499 ms
06-12 23:07:51.781 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471781
06-12 23:07:51.781 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.781 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.781 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471781
06-12 23:07:51.782 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.782 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.782 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.782 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1486.257268 ms
06-12 23:07:51.782 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:07:51.782 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:51.784 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:07:51.784 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:07:51.784 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:07:51.784 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.784 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.785 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:51.785 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.18353109061717987, threshold=0.9, point=(44.0, 324.0)
06-12 23:07:51.785 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.18353109061717987 < threshold=0.9
06-12 23:07:51.785 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1489.471576 ms
06-12 23:07:51.836 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471836
06-12 23:07:51.836 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.836 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471836
06-12 23:07:51.836 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.836 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.836 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.836 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1540.490307 ms
06-12 23:07:51.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744471836
06-12 23:07:51.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:51.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:51.836 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744471836
06-12 23:07:51.836 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:51.836 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:51.836 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.836 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1540.867307 ms
06-12 23:07:51.836 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:07:51.837 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:51.838 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:07:51.838 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:07:51.838 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:07:51.839 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:51.839 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:51.839 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:51.839 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.1953364759683609, threshold=0.9, point=(43.0, 324.0)
06-12 23:07:51.839 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.1953364759683609 >= threshold=0.9? false, brightness=244.40513392857142 < 100? false
06-12 23:07:51.839 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1543.640499 ms
06-12 23:07:51.890 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744471890
06-12 23:07:51.890 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:51.891 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744471890
06-12 23:07:51.891 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:51.891 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:51.891 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:51.891 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:51.891 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:51.891 D/NaApp   (10049): Template cache size: 27
06-12 23:07:51.891 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:51.974 D/NaApp   (10049): Avail: 2175MB | Total: 3558MB
06-12 23:07:51.974 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:51.974 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:51.974 D/NaApp   (10049): compareListImage started iteration 4 at 1678.358461 ms
06-12 23:07:52.025 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:52.025 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:52.025 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:52.026 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:52.026 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:52.026 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:52.042 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:52.044 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:52.045 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:52.052 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:52.053 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:52.058 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:52.059 D/NaApp   (10049): grabScreenshot completed in 85.211231 ms
06-12 23:07:52.150 D/NaApp   (10049): Avail: 2128MB | Total: 3558MB
06-12 23:07:52.150 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:52.150 D/NaApp   (10049): templatePaths.forEach started at 1854.108691 ms
06-12 23:07:52.150 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:52.150 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1854.189653 ms
06-12 23:07:52.150 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472150
06-12 23:07:52.150 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.150 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.150 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472150
06-12 23:07:52.150 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.150 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.150 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.150 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1854.732345 ms
06-12 23:07:52.150 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:52.151 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.151 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.151 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:52.151 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.1138472855091095, threshold=0.9, point=(44.0, 324.0)
06-12 23:07:52.151 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.1138472855091095 < threshold=0.9
06-12 23:07:52.151 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1855.806537 ms
06-12 23:07:52.202 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472202
06-12 23:07:52.202 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.202 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472202
06-12 23:07:52.202 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.202 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.202 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.202 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1906.711037 ms
06-12 23:07:52.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472202
06-12 23:07:52.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.202 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472202
06-12 23:07:52.202 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.202 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.202 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.203 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1907.126422 ms
06-12 23:07:52.203 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:52.203 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.203 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.203 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:52.204 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.12311519682407379, threshold=0.9, point=(43.0, 324.0)
06-12 23:07:52.204 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.12311519682407379 >= threshold=0.9? false, brightness=244.51450892857142 < 100? false
06-12 23:07:52.204 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1908.108114 ms
06-12 23:07:52.255 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472255
06-12 23:07:52.255 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.255 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472255
06-12 23:07:52.255 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.255 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.255 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.255 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:52.255 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:52.255 D/NaApp   (10049): Template cache size: 27
06-12 23:07:52.255 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:52.354 D/NaApp   (10049): Avail: 2066MB | Total: 3558MB
06-12 23:07:52.355 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:52.355 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:52.355 D/NaApp   (10049): compareListImage started iteration 5 at 2058.963499 ms
06-12 23:07:52.418 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:52.418 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:52.418 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:52.418 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:52.418 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:52.418 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:52.426 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:52.427 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:52.428 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:52.432 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:52.432 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:52.439 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:52.440 D/NaApp   (10049): grabScreenshot completed in 84.974462 ms
06-12 23:07:52.515 D/NaApp   (10049): Avail: 2029MB | Total: 3558MB
06-12 23:07:52.515 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:52.515 D/NaApp   (10049): templatePaths.forEach started at 2219.400999 ms
06-12 23:07:52.515 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:52.515 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2219.479306 ms
06-12 23:07:52.515 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472515
06-12 23:07:52.515 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.515 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.515 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472515
06-12 23:07:52.515 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.515 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.515 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.515 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2219.90496 ms
06-12 23:07:52.516 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:07:52.516 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:52.518 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:07:52.518 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:07:52.518 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:07:52.518 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.518 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.518 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:52.519 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.03133377432823181, threshold=0.9, point=(44.0, 324.0)
06-12 23:07:52.519 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.03133377432823181 < threshold=0.9
06-12 23:07:52.519 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2223.201114 ms
06-12 23:07:52.570 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472570
06-12 23:07:52.570 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.570 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472570
06-12 23:07:52.570 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.570 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.570 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.570 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2274.470037 ms
06-12 23:07:52.570 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472570
06-12 23:07:52.570 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.570 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.570 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472570
06-12 23:07:52.570 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.570 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.570 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.570 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2274.860806 ms
06-12 23:07:52.570 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:07:52.571 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:52.573 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:07:52.573 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:07:52.573 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:07:52.573 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.573 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.573 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:52.574 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.04678524658083916, threshold=0.9, point=(43.0, 324.0)
06-12 23:07:52.574 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.04678524658083916 >= threshold=0.9? false, brightness=245.38727678571428 < 100? false
06-12 23:07:52.574 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2278.122268 ms
06-12 23:07:52.624 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472624
06-12 23:07:52.625 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.625 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472624
06-12 23:07:52.625 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.625 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.625 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.625 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:52.625 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:52.625 D/NaApp   (10049): Template cache size: 27
06-12 23:07:52.625 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:52.695 D/NaApp   (10049): Avail: 1998MB | Total: 3558MB
06-12 23:07:52.695 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:52.695 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:52.695 D/NaApp   (10049): compareListImage started iteration 6 at 2399.728498 ms
06-12 23:07:52.746 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:52.746 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:52.747 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:52.747 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:52.747 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:52.747 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:52.760 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:52.760 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:52.764 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:52.769 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:52.770 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:52.770 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:52.771 D/NaApp   (10049): grabScreenshot completed in 75.466653 ms
06-12 23:07:52.836 D/NaApp   (10049): Avail: 1987MB | Total: 3558MB
06-12 23:07:52.836 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:52.836 D/NaApp   (10049): templatePaths.forEach started at 2540.461268 ms
06-12 23:07:52.836 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:52.836 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2540.54796 ms
06-12 23:07:52.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472836
06-12 23:07:52.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.836 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.836 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472836
06-12 23:07:52.836 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.836 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.836 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.837 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2540.955268 ms
06-12 23:07:52.837 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:52.837 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.837 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.837 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:52.837 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.10932401567697525, threshold=0.9, point=(44.0, 322.0)
06-12 23:07:52.837 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.10932401567697525 < threshold=0.9
06-12 23:07:52.837 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2541.855152 ms
06-12 23:07:52.888 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472888
06-12 23:07:52.888 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.888 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472888
06-12 23:07:52.888 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.888 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.888 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.888 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2592.737268 ms
06-12 23:07:52.888 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744472888
06-12 23:07:52.888 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:52.888 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:52.888 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744472888
06-12 23:07:52.888 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:52.888 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:52.888 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.889 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2593.105921 ms
06-12 23:07:52.889 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:52.889 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:52.889 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:52.889 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:52.890 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.09102290123701096, threshold=0.9, point=(43.0, 322.0)
06-12 23:07:52.890 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.09102290123701096 >= threshold=0.9? false, brightness=246.2265625 < 100? false
06-12 23:07:52.890 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2594.129229 ms
06-12 23:07:52.940 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744472940
06-12 23:07:52.940 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:52.940 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744472940
06-12 23:07:52.941 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:52.941 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:52.941 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:52.941 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:52.941 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:52.941 D/NaApp   (10049): Template cache size: 27
06-12 23:07:52.941 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:53.068 D/NaApp   (10049): Avail: 1987MB | Total: 3558MB
06-12 23:07:53.068 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:53.068 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:53.068 D/NaApp   (10049): compareListImage started iteration 7 at 2772.695998 ms
06-12 23:07:53.120 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:53.121 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:53.121 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:53.121 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:53.121 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:53.121 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:53.127 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:53.127 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:53.130 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:53.137 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:53.137 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:53.145 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:53.147 D/NaApp   (10049): grabScreenshot completed in 78.04323 ms
06-12 23:07:53.277 D/NaApp   (10049): Avail: 1986MB | Total: 3558MB
06-12 23:07:53.277 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:53.277 D/NaApp   (10049): templatePaths.forEach started at 2981.729844 ms
06-12 23:07:53.277 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:53.278 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2981.946152 ms
06-12 23:07:53.278 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744473278
06-12 23:07:53.278 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:53.278 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:53.278 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744473278
06-12 23:07:53.278 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:53.278 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:53.278 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.279 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2982.971998 ms
06-12 23:07:53.279 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:07:53.279 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:53.283 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:07:53.284 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:07:53.284 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:07:53.284 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:53.284 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:53.285 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:53.286 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.08406226336956024, threshold=0.9, point=(18.0, 310.0)
06-12 23:07:53.286 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.08406226336956024 < threshold=0.9
06-12 23:07:53.286 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2990.286575 ms
06-12 23:07:53.338 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744473338
06-12 23:07:53.338 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:53.338 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744473338
06-12 23:07:53.338 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:53.338 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:53.338 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.338 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3042.375267 ms
06-12 23:07:53.338 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744473338
06-12 23:07:53.338 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:53.338 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:53.338 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744473338
06-12 23:07:53.338 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:53.338 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:53.338 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.339 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3043.163729 ms
06-12 23:07:53.339 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:07:53.339 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:53.342 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:07:53.342 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:07:53.343 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:07:53.343 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:53.343 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:53.343 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:53.344 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.11971547454595566, threshold=0.9, point=(43.0, 318.0)
06-12 23:07:53.344 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.11971547454595566 >= threshold=0.9? false, brightness=246.9955357142857 < 100? false
06-12 23:07:53.344 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3048.693383 ms
06-12 23:07:53.396 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744473396
06-12 23:07:53.396 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:53.397 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744473396
06-12 23:07:53.397 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:53.398 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:53.398 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.399 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:53.399 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:53.399 D/NaApp   (10049): Template cache size: 27
06-12 23:07:53.399 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:53.494 D/NaApp   (10049): Avail: 1997MB | Total: 3558MB
06-12 23:07:53.495 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:53.495 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:53.495 D/NaApp   (10049): compareListImage started iteration 8 at 3198.985921 ms
06-12 23:07:53.546 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:53.546 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:53.546 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:53.546 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:53.546 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:53.547 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:53.646 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:53.648 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:53.658 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:53.685 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:53.686 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:53.690 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:53.692 D/NaApp   (10049): grabScreenshot completed in 197.200807 ms
06-12 23:07:53.783 D/NaApp   (10049): Avail: 2059MB | Total: 3558MB
06-12 23:07:53.783 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:53.783 D/NaApp   (10049): templatePaths.forEach started at 3487.578305 ms
06-12 23:07:53.783 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:53.783 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3487.713998 ms
06-12 23:07:53.783 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744473783
06-12 23:07:53.784 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:53.784 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:53.784 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744473783
06-12 23:07:53.784 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:53.784 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:53.784 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.784 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3488.551267 ms
06-12 23:07:53.784 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:53.785 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:53.785 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:53.785 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:53.786 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.08164173364639282, threshold=0.9, point=(18.0, 310.0)
06-12 23:07:53.786 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.08164173364639282 < threshold=0.9
06-12 23:07:53.786 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3490.183344 ms
06-12 23:07:53.837 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744473837
06-12 23:07:53.837 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:53.837 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744473837
06-12 23:07:53.837 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:53.837 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:53.837 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.837 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3541.620805 ms
06-12 23:07:53.837 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744473837
06-12 23:07:53.837 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:53.837 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:53.837 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744473837
06-12 23:07:53.837 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:53.837 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:53.837 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.838 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3542.123959 ms
06-12 23:07:53.838 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:07:53.838 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:53.838 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:53.838 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:07:53.839 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.10132086277008057, threshold=0.9, point=(33.0, 310.0)
06-12 23:07:53.839 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.10132086277008057 >= threshold=0.9? false, brightness=247.11941964285714 < 100? false
06-12 23:07:53.839 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3543.530575 ms
06-12 23:07:53.893 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744473892
06-12 23:07:53.893 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:53.893 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744473892
06-12 23:07:53.893 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:53.893 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:53.893 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:53.893 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:53.893 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:53.893 D/NaApp   (10049): Template cache size: 27
06-12 23:07:53.893 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:54.008 D/NaApp   (10049): Avail: 2044MB | Total: 3558MB
06-12 23:07:54.008 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:54.008 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:54.008 D/NaApp   (10049): compareListImage started iteration 9 at 3712.529305 ms
06-12 23:07:54.059 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:54.060 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:54.060 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:54.061 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:54.061 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:54.061 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:54.207 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:54.208 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:54.214 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:54.236 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:54.237 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:54.238 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:54.240 D/NaApp   (10049): grabScreenshot completed in 231.024385 ms
06-12 23:07:54.388 D/NaApp   (10049): Avail: 2006MB | Total: 3558MB
06-12 23:07:54.388 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:07:54.388 D/NaApp   (10049): templatePaths.forEach started at 4092.09292 ms
06-12 23:07:54.388 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:54.388 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4092.256343 ms
06-12 23:07:54.388 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744474388
06-12 23:07:54.388 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:54.388 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:54.388 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744474388
06-12 23:07:54.388 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:54.388 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:54.388 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:54.389 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4093.457997 ms
06-12 23:07:54.389 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:07:54.389 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:07:54.395 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:07:54.395 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:07:54.397 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:07:54.399 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:07:54.399 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:07:54.400 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:07:54.401 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.9723406434059143, threshold=0.9, point=(26.0, 324.0)
06-12 23:07:54.401 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/home.png with maxVal=0.9723406434059143 >= threshold=0.9
06-12 23:07:54.401 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4105.250459 ms
06-12 23:07:54.401 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/home.png
06-12 23:07:54.401 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744474401
06-12 23:07:54.401 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:54.401 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744474401
06-12 23:07:54.401 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:54.401 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:54.401 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:54.401 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:54.401 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:54.401 D/NaApp   (10049): Template cache size: 27
06-12 23:07:54.402 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:54.402 D/NaApp   (10049): Match found at iteration 9: Img/SMG935F/home.png
06-12 23:07:54.402 D/NaApp   (10049): Match already found, stopping at iteration 10
06-12 23:07:54.402 D/NaApp   (10049): Match already found, stopping at iteration 11
06-12 23:07:54.402 D/NaApp   (10049): Match already found, stopping at iteration 12
06-12 23:07:54.402 D/NaApp   (10049): Match already found, stopping at iteration 13
06-12 23:07:54.402 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:07:54.402 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:54.402 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:54.402 D/NaApp   (10049): Template cache size: 27
06-12 23:07:54.402 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:07:54.402 D/NaApp   (10049): compareImagesLong result at 4106.57592 ms: DONE
06-12 23:07:54.402 D/NaApp   (10049): checkLoadGame: compareImagesLong completed in 4106.715151 ms
06-12 23:07:54.402 D/NaApp   (10049): Check load game: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/home.png, point=Point(x=26, y=324)))
06-12 23:07:54.403 D/NaApp   (10049): Buying field
06-12 23:07:59.083 D/NaApp   (10049): Avail: 2044MB | Total: 3558MB
06-12 23:07:59.083 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:07:59.083 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:07:59.083 D/NaApp   (10049): compareListImage started iteration 0 at 8787.243994 ms
06-12 23:07:59.135 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:07:59.136 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:07:59.136 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:07:59.136 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:07:59.136 D/NaApp   (10049): Triggering single screen capture
06-12 23:07:59.136 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:07:59.144 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:07:59.144 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:07:59.146 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:07:59.153 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:07:59.154 D/NaApp   (10049): Controlled capture completed: true
06-12 23:07:59.162 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:07:59.163 D/NaApp   (10049): grabScreenshot completed in 80.08877 ms
06-12 23:07:59.278 D/NaApp   (10049): Avail: 2043MB | Total: 3558MB
06-12 23:07:59.278 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:07:59.278 D/NaApp   (10049): templatePaths.forEach started at 8982.816955 ms
06-12 23:07:59.279 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:07:59.279 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 8982.963609 ms
06-12 23:07:59.279 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744479279
06-12 23:07:59.279 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:07:59.279 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:07:59.279 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744479279
06-12 23:07:59.279 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:07:59.279 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:07:59.279 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:59.280 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 8984.014071 ms
06-12 23:07:59.280 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:07:59.280 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:07:59.280 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:07:59.280 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:07:59.281 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:07:59.281 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:07:59.281 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 8985.363801 ms
06-12 23:07:59.281 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:07:59.281 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744479281
06-12 23:07:59.281 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:07:59.281 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744479281
06-12 23:07:59.281 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:07:59.281 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:07:59.281 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:07:59.281 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:59.281 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:59.281 D/NaApp   (10049): Template cache size: 27
06-12 23:07:59.281 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:07:59.283 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:07:59.283 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:07:59.283 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:07:59.283 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:07:59.283 D/NaApp   (10049): Template cache size: 27
06-12 23:07:59.283 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:07:59.283 D/NaApp   (10049): compareImagesLong result at 8987.121494 ms: DONE
06-12 23:07:59.283 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:02.023 D/NaApp   (10049): Avail: 1978MB | Total: 3558MB
06-12 23:08:02.023 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:02.023 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:02.023 D/NaApp   (10049): compareListImage started iteration 0 at 11727.155492 ms
06-12 23:08:02.074 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:02.074 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:02.074 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:02.075 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:02.075 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:02.075 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:02.076 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:02.076 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:02.078 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:02.093 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:02.093 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:02.094 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:02.095 D/NaApp   (10049): grabScreenshot completed in 72.040116 ms
06-12 23:08:02.201 D/NaApp   (10049): Avail: 1978MB | Total: 3558MB
06-12 23:08:02.202 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:02.202 D/NaApp   (10049): templatePaths.forEach started at 11905.953146 ms
06-12 23:08:02.202 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:02.202 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 11906.193915 ms
06-12 23:08:02.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744482202
06-12 23:08:02.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:02.202 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:02.202 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744482202
06-12 23:08:02.202 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:02.202 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:02.202 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:02.203 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 11907.276146 ms
06-12 23:08:02.203 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/BuySPC.png
06-12 23:08:02.203 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:02.207 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/BuySPC.png (23x23)
06-12 23:08:02.207 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/BuySPC.png, size=23x23
06-12 23:08:02.208 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/BuySPC.png, original size=23x23, scaled size=17x17, scale=0.75
06-12 23:08:02.208 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:02.208 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:02.208 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:02.209 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:02.209 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:02.209 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 11913.411799 ms
06-12 23:08:02.209 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:02.209 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744482209
06-12 23:08:02.209 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:02.209 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744482209
06-12 23:08:02.209 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:02.209 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:02.209 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:02.210 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:02.210 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:02.210 D/NaApp   (10049): Template cache size: 27
06-12 23:08:02.210 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:02.210 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:02.210 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:02.210 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:02.210 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:02.210 D/NaApp   (10049): Template cache size: 27
06-12 23:08:02.210 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:02.210 D/NaApp   (10049): compareImagesLong result at 11914.483761 ms: DONE
06-12 23:08:02.211 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:04.942 D/NaApp   (10049): Avail: 1976MB | Total: 3558MB
06-12 23:08:04.942 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:04.943 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:04.943 D/NaApp   (10049): compareListImage started iteration 0 at 14646.983374 ms
06-12 23:08:04.994 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:04.995 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:04.995 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:04.995 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:04.995 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:04.995 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:05.010 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:05.010 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:05.013 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:05.024 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:05.024 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:05.026 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:05.028 D/NaApp   (10049): grabScreenshot completed in 84.465308 ms
06-12 23:08:05.135 D/NaApp   (10049): Avail: 1978MB | Total: 3558MB
06-12 23:08:05.135 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:05.135 D/NaApp   (10049): templatePaths.forEach started at 14839.892297 ms
06-12 23:08:05.136 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:05.136 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 14840.067566 ms
06-12 23:08:05.136 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744485136
06-12 23:08:05.136 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:05.136 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:05.136 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744485136
06-12 23:08:05.136 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:05.136 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:05.136 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:05.137 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 14841.020566 ms
06-12 23:08:05.137 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:05.137 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:05.137 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:05.137 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:05.138 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:05.138 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:05.138 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 14842.311643 ms
06-12 23:08:05.138 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:05.138 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744485138
06-12 23:08:05.138 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:05.138 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744485138
06-12 23:08:05.138 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:05.138 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:05.138 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:05.138 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:05.138 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:05.138 D/NaApp   (10049): Template cache size: 27
06-12 23:08:05.138 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:05.139 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:05.139 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:05.139 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:05.139 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:05.139 D/NaApp   (10049): Template cache size: 27
06-12 23:08:05.139 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:05.139 D/NaApp   (10049): compareImagesLong result at 14843.314336 ms: DONE
06-12 23:08:05.139 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:07.884 D/NaApp   (10049): Avail: 1978MB | Total: 3558MB
06-12 23:08:07.885 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:07.885 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:07.885 D/NaApp   (10049): compareListImage started iteration 0 at 17589.03418 ms
06-12 23:08:07.936 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:07.937 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:07.937 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:07.937 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:07.937 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:07.938 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:07.948 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:07.948 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:07.951 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:07.965 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:07.965 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:07.978 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:07.980 D/NaApp   (10049): grabScreenshot completed in 95.065115 ms
06-12 23:08:08.095 D/NaApp   (10049): Avail: 1979MB | Total: 3558MB
06-12 23:08:08.095 D/NaApp   (10049): App Pss: 66 MB | Private Dirty: 34 MB
06-12 23:08:08.095 D/NaApp   (10049): templatePaths.forEach started at 17799.638757 ms
06-12 23:08:08.095 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:08.095 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 17799.805295 ms
06-12 23:08:08.096 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744488095
06-12 23:08:08.096 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:08.096 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:08.096 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744488095
06-12 23:08:08.096 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:08.096 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:08.096 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:08.097 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 17800.915795 ms
06-12 23:08:08.097 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/BuySPC.png
06-12 23:08:08.097 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:08.099 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/BuySPC.png (23x23)
06-12 23:08:08.099 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/BuySPC.png, size=23x23
06-12 23:08:08.099 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/BuySPC.png, original size=23x23, scaled size=17x17, scale=0.75
06-12 23:08:08.100 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:08.100 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:08.100 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:08.100 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:08.100 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:08.101 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 17804.957872 ms
06-12 23:08:08.101 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:08.101 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744488101
06-12 23:08:08.101 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:08.101 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744488101
06-12 23:08:08.101 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:08.101 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:08.101 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:08.101 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:08.101 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:08.101 D/NaApp   (10049): Template cache size: 27
06-12 23:08:08.101 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:08.101 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:08.101 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:08.102 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:08.102 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:08.102 D/NaApp   (10049): Template cache size: 27
06-12 23:08:08.102 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:08.102 D/NaApp   (10049): compareImagesLong result at 17806.188218 ms: DONE
06-12 23:08:08.102 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:10.829 D/NaApp   (10049): Avail: 1979MB | Total: 3558MB
06-12 23:08:10.830 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:10.830 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:10.830 D/NaApp   (10049): compareListImage started iteration 0 at 20534.011024 ms
06-12 23:08:10.881 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:10.882 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:10.882 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:10.882 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:10.883 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:10.883 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:10.894 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:10.895 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:10.898 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:10.906 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:10.906 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:10.908 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:10.911 D/NaApp   (10049): grabScreenshot completed in 80.717115 ms
06-12 23:08:11.029 D/NaApp   (10049): Avail: 1979MB | Total: 3558MB
06-12 23:08:11.029 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:11.029 D/NaApp   (10049): templatePaths.forEach started at 20733.864216 ms
06-12 23:08:11.030 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:11.030 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 20734.058332 ms
06-12 23:08:11.030 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744491030
06-12 23:08:11.030 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:11.030 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:11.030 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744491030
06-12 23:08:11.030 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:11.030 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:11.030 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:11.031 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 20735.212678 ms
06-12 23:08:11.031 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:11.031 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:11.031 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:11.031 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:11.032 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:11.032 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:11.032 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 20736.638139 ms
06-12 23:08:11.032 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:11.032 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744491032
06-12 23:08:11.033 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:11.033 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744491032
06-12 23:08:11.033 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:11.033 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:11.033 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:11.033 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:11.033 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:11.033 D/NaApp   (10049): Template cache size: 27
06-12 23:08:11.033 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:11.033 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:11.033 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:11.033 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:11.033 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:11.033 D/NaApp   (10049): Template cache size: 27
06-12 23:08:11.033 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:11.033 D/NaApp   (10049): compareImagesLong result at 20737.596562 ms: DONE
06-12 23:08:11.033 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:13.773 D/NaApp   (10049): Avail: 2005MB | Total: 3558MB
06-12 23:08:13.773 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:13.773 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:13.773 D/NaApp   (10049): compareListImage started iteration 0 at 23477.24406 ms
06-12 23:08:13.824 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:13.825 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:13.825 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:13.825 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:13.825 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:13.825 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:13.841 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:13.842 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:13.844 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:13.850 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:13.850 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:13.852 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:13.854 D/NaApp   (10049): grabScreenshot completed in 80.868884 ms
06-12 23:08:14.012 D/NaApp   (10049): Avail: 2005MB | Total: 3558MB
06-12 23:08:14.012 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:14.012 D/NaApp   (10049): templatePaths.forEach started at 23716.574829 ms
06-12 23:08:14.012 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:14.012 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 23716.744676 ms
06-12 23:08:14.012 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744494012
06-12 23:08:14.013 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:14.013 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:14.013 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744494012
06-12 23:08:14.013 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:14.013 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:14.013 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:14.013 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 23717.562137 ms
06-12 23:08:14.013 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/BuySPC.png
06-12 23:08:14.013 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:14.024 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/BuySPC.png (23x23)
06-12 23:08:14.025 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/BuySPC.png, size=23x23
06-12 23:08:14.025 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/BuySPC.png, original size=23x23, scaled size=17x17, scale=0.75
06-12 23:08:14.025 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:14.025 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:14.025 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:14.026 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:14.026 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:14.026 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 23730.14056 ms
06-12 23:08:14.026 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:14.026 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744494026
06-12 23:08:14.026 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:14.026 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744494026
06-12 23:08:14.026 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:14.026 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:14.026 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:14.026 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:14.026 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:14.026 D/NaApp   (10049): Template cache size: 27
06-12 23:08:14.026 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:14.026 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:14.026 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:14.026 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:14.026 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:14.026 D/NaApp   (10049): Template cache size: 27
06-12 23:08:14.026 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:14.027 D/NaApp   (10049): compareImagesLong result at 23730.935983 ms: DONE
06-12 23:08:14.027 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:16.790 D/NaApp   (10049): Avail: 2003MB | Total: 3558MB
06-12 23:08:16.790 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:16.790 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:16.791 D/NaApp   (10049): compareListImage started iteration 0 at 26494.89975 ms
06-12 23:08:16.841 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:16.842 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:16.842 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:16.842 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:16.842 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:16.842 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:16.858 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:16.858 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:16.859 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:16.864 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:16.864 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:16.872 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:16.872 D/NaApp   (10049): grabScreenshot completed in 81.436807 ms
06-12 23:08:16.950 D/NaApp   (10049): Avail: 2003MB | Total: 3558MB
06-12 23:08:16.950 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:16.950 D/NaApp   (10049): templatePaths.forEach started at 26654.536135 ms
06-12 23:08:16.950 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:16.950 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 26654.629327 ms
06-12 23:08:16.950 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744496950
06-12 23:08:16.950 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:16.950 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:16.950 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744496950
06-12 23:08:16.950 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:16.950 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:16.950 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:16.951 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 26655.001443 ms
06-12 23:08:16.951 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:16.951 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:16.951 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:16.951 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:16.951 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.9181495904922485, threshold=0.9, point=(81.33333333333333, 152.66666666666666)
06-12 23:08:16.951 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC.png with maxVal=0.9181495904922485 >= threshold=0.9
06-12 23:08:16.951 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 26655.612135 ms
06-12 23:08:16.951 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC.png
06-12 23:08:16.951 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744496951
06-12 23:08:16.951 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:16.951 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744496951
06-12 23:08:16.951 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:16.951 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:16.951 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:16.951 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:16.951 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:16.951 D/NaApp   (10049): Template cache size: 27
06-12 23:08:16.951 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:16.952 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC.png
06-12 23:08:16.952 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:16.952 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:16.952 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:16.952 D/NaApp   (10049): Template cache size: 27
06-12 23:08:16.952 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:16.952 D/NaApp   (10049): compareImagesLong result at 26656.158943 ms: DONE
06-12 23:08:16.952 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC.png, point=Point(x=81, y=152)))
06-12 23:08:19.651 D/NaApp   (10049): Avail: 2003MB | Total: 3558MB
06-12 23:08:19.651 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:08:19.651 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:19.651 D/NaApp   (10049): compareListImage started iteration 0 at 29355.203402 ms
06-12 23:08:19.702 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:19.702 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:19.702 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:19.702 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:19.702 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:19.702 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:19.708 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:19.708 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:19.710 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:19.714 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:19.714 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:19.716 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:19.717 D/NaApp   (10049): grabScreenshot completed in 65.554616 ms
06-12 23:08:19.756 D/NaApp   (10049): Avail: 2003MB | Total: 3558MB
06-12 23:08:19.756 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:19.756 D/NaApp   (10049): templatePaths.forEach started at 29460.331595 ms
06-12 23:08:19.756 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:19.756 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC.png 29460.42971 ms
06-12 23:08:19.756 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744499756
06-12 23:08:19.756 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:19.756 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:19.756 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744499756
06-12 23:08:19.756 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:19.756 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:19.756 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:19.756 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC.png using slot 0 29460.784825 ms
06-12 23:08:19.756 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/BuySPC.png
06-12 23:08:19.756 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/BuySPC.png (scale: 0.75)
06-12 23:08:19.757 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/BuySPC.png (23x23)
06-12 23:08:19.757 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/BuySPC.png, size=23x23
06-12 23:08:19.757 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/BuySPC.png, original size=23x23, scaled size=17x17, scale=0.75
06-12 23:08:19.758 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:19.758 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:19.758 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC.png - scaledRoiMat size=18x20 (18x20), template size=17x17 (17x17), scale=0.75, gray=false
06-12 23:08:19.758 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC.png, maxVal=0.07585400342941284, threshold=0.9, point=(82.66666666666667, 155.33333333333334)
06-12 23:08:19.758 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/BuySPC.png maxVal=0.07585400342941284 < threshold=0.9
06-12 23:08:19.758 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC.png slot 0 29462.411518 ms
06-12 23:08:19.809 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744499809
06-12 23:08:19.809 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:19.809 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744499809
06-12 23:08:19.809 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:19.809 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:19.809 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:19.809 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/BuySPC_Done.png 29513.799325 ms
06-12 23:08:19.810 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744499809
06-12 23:08:19.810 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:19.810 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:19.810 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744499809
06-12 23:08:19.810 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:19.810 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:19.810 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:19.811 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/BuySPC_Done.png using slot 0 29515.088941 ms
06-12 23:08:19.811 D/NaApp   (10049): Cache hit: Img/SMG935F/BuySPC_Done.png (scale: 0.75)
06-12 23:08:19.811 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/BuySPC_Done.png - original rect={70, 140, 25x27}, valid rect={70, 140, 25x27}, roiMat size=25x27 (25x27)
06-12 23:08:19.811 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/BuySPC_Done.png - original size=25x27 (25x27), scale=0.75, newSize=18x20 (18x20)
06-12 23:08:19.811 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/BuySPC_Done.png - scaledRoiMat size=18x20 (18x20), template size=18x17 (18x17), scale=0.75, gray=false
06-12 23:08:19.812 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/BuySPC_Done.png, maxVal=0.9190098643302917, threshold=0.9, point=(82.0, 152.66666666666666)
06-12 23:08:19.812 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/BuySPC_Done.png with maxVal=0.9190098643302917 >= threshold=0.9
06-12 23:08:19.812 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/BuySPC_Done.png slot 0 29516.533864 ms
06-12 23:08:19.812 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/BuySPC_Done.png
06-12 23:08:19.812 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744499812
06-12 23:08:19.812 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:19.812 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744499812
06-12 23:08:19.812 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:19.813 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:19.813 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:19.813 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:19.813 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:19.813 D/NaApp   (10049): Template cache size: 27
06-12 23:08:19.813 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:19.813 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/BuySPC_Done.png
06-12 23:08:19.813 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:19.813 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:19.813 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:19.813 D/NaApp   (10049): Template cache size: 27
06-12 23:08:19.813 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:19.813 D/NaApp   (10049): compareImagesLong result at 29517.642633 ms: DONE
06-12 23:08:19.813 D/NaApp   (10049): Check buy field: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/BuySPC_Done.png, point=Point(x=82, y=152)))
06-12 23:08:20.921 D/NaApp   (10049): Receiving EXP achievement
06-12 23:08:28.713 D/NaApp   (10049): Harvesting field
06-12 23:08:36.844 D/NaApp   (10049): Checking level up
06-12 23:08:36.953 D/NaApp   (10049): Avail: 2014MB | Total: 3558MB
06-12 23:08:36.953 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:36.953 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:36.953 D/NaApp   (10049): compareListImage started iteration 0 at 46657.464505 ms
06-12 23:08:37.005 D/NaApp   (10049): 🔄 Sequential processing 3 templates
06-12 23:08:37.005 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:37.005 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:37.006 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:37.006 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:37.006 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:37.009 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:37.009 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:37.011 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:37.025 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:37.026 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:37.029 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:37.031 D/NaApp   (10049): grabScreenshot completed in 77.224769 ms
06-12 23:08:37.183 D/NaApp   (10049): Avail: 2015MB | Total: 3558MB
06-12 23:08:37.183 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:37.183 D/NaApp   (10049): templatePaths.forEach started at 46887.148851 ms
06-12 23:08:37.183 D/NaApp   (10049): Processing 3 templates (limited to 3)
06-12 23:08:37.183 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/levelup.png 46887.304159 ms
06-12 23:08:37.183 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744517183
06-12 23:08:37.183 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:37.183 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:37.183 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744517183
06-12 23:08:37.183 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:37.183 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:37.183 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:37.184 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/levelup.png using slot 0 46888.019813 ms
06-12 23:08:37.184 D/NaApp   (10049): Cache hit: Img/SMG935F/levelup.png (scale: 0.25)
06-12 23:08:37.184 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/levelup.png - original rect={212, 27, 221x50}, valid rect={212, 27, 221x50}, roiMat size=221x50 (221x50)
06-12 23:08:37.184 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/levelup.png - original size=221x50 (221x50), scale=0.25, newSize=55x12 (55x12)
06-12 23:08:37.184 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/levelup.png - scaledRoiMat size=55x12 (55x12), template size=48x8 (48x8), scale=0.25, gray=false
06-12 23:08:37.198 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/levelup.png, maxVal=0.11744669079780579, threshold=0.9, point=(312.0, 47.0)
06-12 23:08:37.198 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/levelup.png maxVal=0.11744669079780579 < threshold=0.9
06-12 23:08:37.198 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/levelup.png slot 0 46902.892044 ms
06-12 23:08:37.250 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744517249
06-12 23:08:37.250 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:37.250 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744517249
06-12 23:08:37.250 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:37.250 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:37.250 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:37.250 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 46954.202428 ms
06-12 23:08:37.250 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744517250
06-12 23:08:37.250 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:37.250 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:37.250 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744517250
06-12 23:08:37.250 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:37.250 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:37.250 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:37.251 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 46955.102774 ms
06-12 23:08:37.251 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:08:37.251 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:08:37.251 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:08:37.252 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:08:37.253 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.9664503335952759, threshold=0.9, point=(26.0, 324.0)
06-12 23:08:37.253 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/home.png with maxVal=0.9664503335952759 >= threshold=0.9
06-12 23:08:37.253 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 46957.043697 ms
06-12 23:08:37.253 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/home.png
06-12 23:08:37.253 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744517253
06-12 23:08:37.253 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:37.253 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744517253
06-12 23:08:37.253 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:37.253 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:37.253 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:37.253 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:37.253 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:37.253 D/NaApp   (10049): Template cache size: 27
06-12 23:08:37.253 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:37.253 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/home.png
06-12 23:08:37.253 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:37.253 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:37.253 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:37.253 D/NaApp   (10049): Template cache size: 27
06-12 23:08:37.253 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:37.253 D/NaApp   (10049): compareImagesLong result at 46957.88712 ms: DONE
06-12 23:08:37.254 D/NaApp   (10049): Check level up: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/home.png, point=Point(x=26, y=324)))
06-12 23:08:37.359 D/NaApp   (10049): Growing field
06-12 23:08:44.315 D/NaApp   (10049): Opening shop
06-12 23:08:44.826 D/NaApp   (10049): openShop: compareImagesLong started at 987144.203486 ms
06-12 23:08:44.950 D/NaApp   (10049): Avail: 2012MB | Total: 3558MB
06-12 23:08:44.950 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:44.950 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:08:44.951 D/NaApp   (10049): compareListImage started iteration 0 at 124.667577 ms
06-12 23:08:45.002 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:08:45.002 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:08:45.002 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:08:45.003 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:08:45.003 D/NaApp   (10049): Triggering single screen capture
06-12 23:08:45.003 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:08:45.012 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:08:45.012 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:08:45.014 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:08:45.028 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:08:45.028 D/NaApp   (10049): Controlled capture completed: true
06-12 23:08:45.029 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:08:45.031 D/NaApp   (10049): grabScreenshot completed in 80.228308 ms
06-12 23:08:45.146 D/NaApp   (10049): Avail: 2014MB | Total: 3558MB
06-12 23:08:45.146 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:08:45.146 D/NaApp   (10049): templatePaths.forEach started at 320.061423 ms
06-12 23:08:45.146 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:08:45.146 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/shopfirst.png 320.223346 ms
06-12 23:08:45.146 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744525146
06-12 23:08:45.146 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:08:45.146 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:08:45.146 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744525146
06-12 23:08:45.146 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:08:45.146 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:08:45.146 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:45.147 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/shopfirst.png using slot 0 321.129538 ms
06-12 23:08:45.147 D/NaApp   (10049): Cache hit: Img/SMG935F/shopfirst.png (scale: 1.0)
06-12 23:08:45.147 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/shopfirst.png - original rect={163, 82, 13x13}, valid rect={163, 82, 13x13}, roiMat size=13x13 (13x13)
06-12 23:08:45.147 D/NaApp   (10049): ROI no scaling slot 0: Img/SMG935F/shopfirst.png - size=13x13 (13x13)
06-12 23:08:45.147 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/shopfirst.png - scaledRoiMat size=13x13 (13x13), template size=11x10 (11x10), scale=1.0, gray=false
06-12 23:08:45.148 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/shopfirst.png, maxVal=0.930794894695282, threshold=0.9, point=(170.5, 90.0)
06-12 23:08:45.148 D/NaApp   (10049): 🎉 SUCCESS: Template match FOUND! Img/SMG935F/shopfirst.png with maxVal=0.930794894695282 >= threshold=0.9
06-12 23:08:45.148 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/shopfirst.png slot 0 322.259654 ms
06-12 23:08:45.148 D/NaApp   (10049): MATCH FOUND: Img/SMG935F/shopfirst.png
06-12 23:08:45.148 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744525148
06-12 23:08:45.148 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:08:45.148 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744525148
06-12 23:08:45.148 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:08:45.148 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:08:45.148 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:08:45.149 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:45.149 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:45.149 D/NaApp   (10049): Template cache size: 27
06-12 23:08:45.149 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:08:45.149 D/NaApp   (10049): Match found at iteration 0: Img/SMG935F/shopfirst.png
06-12 23:08:45.149 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:08:45.149 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:08:45.149 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:08:45.149 D/NaApp   (10049): Template cache size: 27
06-12 23:08:45.149 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:08:45.149 D/NaApp   (10049): compareImagesLong result at 323.195461 ms: DONE
06-12 23:08:45.149 D/NaApp   (10049): openShop: compareImagesLong completed in 323.609192 ms
06-12 23:08:45.150 D/NaApp   (10049): Opening Shop: (DONE, TemplateMatchResult(templatePath=Img/SMG935F/shopfirst.png, point=Point(x=170, y=90)))
06-12 23:08:45.764 D/NaApp   (10049): Level set to 1
06-12 23:08:56.763 D/NaApp   (10049): Automation cycle completed
06-12 23:08:56.957 D/NaApp   (10049): Auto index: 1
06-12 23:08:57.065 D/NaApp   (10049): Avail: 2016MB | Total: 3558MB
06-12 23:08:57.065 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:08:57.427 D/NaApp   (10049): CPU Usage: 48%
06-12 23:08:57.432 D/NaApp   (10049): Folder already exists: /storage/emulated/0/Dragon/Farm
06-12 23:08:57.435 W/NaApp   (10049): Go Home
06-12 23:08:58.293 D/NaApp   (10049): Command success: am force-stop com.supercell.hayday
06-12 23:08:58.424 D/NaApp   (10049): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:08:58.494 D/NaApp   (10049): Command success: service call SurfaceFlinger 1008 i32 0
06-12 23:08:58.604 D/NaApp   (10049): Command success: sync
06-12 23:08:58.975 D/NaApp   (10049): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 23:08:59.195 D/NaApp   (10049): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 23:08:59.255 D/NaApp   (10049): Command success: pm trim-caches 100M
06-12 23:08:59.316 D/NaApp   (10049): Command success: am kill-all
06-12 23:08:59.316 D/NaApp   (10049): Killed com.supercell.hayday
06-12 23:09:00.762 D/NaApp   (10049): Successfully applied Farm #2
06-12 23:09:09.359 D/NaApp   (10049): Open Farm: 2 / 10
06-12 23:09:09.368 D/NaApp   (10049): Farm opened successfully
06-12 23:09:09.369 D/NaApp   (10049): Checking game load
06-12 23:09:09.369 D/NaApp   (10049): checkLoadGame: compareImagesLong started at 1011687.563545 ms
06-12 23:09:09.551 D/NaApp   (10049): Avail: 2302MB | Total: 3558MB
06-12 23:09:09.551 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:09.551 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:09.551 D/NaApp   (10049): compareListImage started iteration 0 at 182.111346 ms
06-12 23:09:09.602 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:09.602 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:09.602 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:09.603 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:09.603 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:09.603 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:09.619 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:09.619 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:09.621 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:09.628 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:09.628 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:09.630 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:09.630 D/NaApp   (10049): grabScreenshot completed in 78.855692 ms
06-12 23:09:09.734 D/NaApp   (10049): Avail: 2296MB | Total: 3558MB
06-12 23:09:09.734 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:09.734 D/NaApp   (10049): templatePaths.forEach started at 364.533346 ms
06-12 23:09:09.734 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:09.734 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 364.630308 ms
06-12 23:09:09.734 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744549734
06-12 23:09:09.734 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:09.734 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:09.734 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744549734
06-12 23:09:09.734 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:09.734 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:09.734 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:09.734 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 365.0335 ms
06-12 23:09:09.734 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:09.734 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:09.738 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:09.738 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:09.738 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:09.739 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:09.739 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:09.739 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:09.739 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.304922491312027, threshold=0.9, point=(40.0, 324.0)
06-12 23:09:09.739 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.304922491312027 < threshold=0.9
06-12 23:09:09.739 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 370.196154 ms
06-12 23:09:09.790 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744549790
06-12 23:09:09.790 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:09.790 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744549790
06-12 23:09:09.790 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:09.790 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:09.790 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:09.790 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 421.084923 ms
06-12 23:09:09.790 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744549790
06-12 23:09:09.790 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:09.790 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:09.790 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744549790
06-12 23:09:09.790 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:09.790 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:09.790 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:09.791 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 421.474769 ms
06-12 23:09:09.791 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:09.791 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:09.793 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:09.793 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:09.793 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:09.793 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:09.793 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:09.793 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:09.794 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.29913508892059326, threshold=0.9, point=(39.0, 324.0)
06-12 23:09:09.794 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.29913508892059326 >= threshold=0.9? false, brightness=242.234375 < 100? false
06-12 23:09:09.794 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 424.945308 ms
06-12 23:09:09.845 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744549845
06-12 23:09:09.845 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:09.845 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744549845
06-12 23:09:09.845 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:09.845 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:09.845 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:09.845 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:09.845 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:09.845 D/NaApp   (10049): Template cache size: 27
06-12 23:09:09.845 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:09.909 D/NaApp   (10049): Avail: 2290MB | Total: 3558MB
06-12 23:09:09.909 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:09.909 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:09.909 D/NaApp   (10049): compareListImage started iteration 1 at 539.949269 ms
06-12 23:09:09.961 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:09.961 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:09.961 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:09.962 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:09.962 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:09.962 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:09.962 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:09.962 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:09.964 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:09.972 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:09.972 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:09.973 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:09.974 D/NaApp   (10049): grabScreenshot completed in 64.531769 ms
06-12 23:09:10.015 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:10.015 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:10.015 D/NaApp   (10049): templatePaths.forEach started at 645.8475 ms
06-12 23:09:10.015 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:10.015 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 645.942308 ms
06-12 23:09:10.015 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550015
06-12 23:09:10.015 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.015 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.015 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550015
06-12 23:09:10.015 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.015 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.015 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.016 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 646.412884 ms
06-12 23:09:10.016 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:10.016 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.016 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.016 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:10.016 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.25092747807502747, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:10.016 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.25092747807502747 < threshold=0.9
06-12 23:09:10.016 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 647.290154 ms
06-12 23:09:10.067 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550067
06-12 23:09:10.067 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.067 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550067
06-12 23:09:10.067 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.067 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.067 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.067 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 698.059308 ms
06-12 23:09:10.067 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550067
06-12 23:09:10.067 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.067 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.067 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550067
06-12 23:09:10.067 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.067 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.067 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.068 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 698.397115 ms
06-12 23:09:10.068 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:10.068 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.068 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.068 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:10.068 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.240432471036911, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:10.068 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.240432471036911 >= threshold=0.9? false, brightness=242.74888392857142 < 100? false
06-12 23:09:10.068 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 699.265538 ms
06-12 23:09:10.119 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550119
06-12 23:09:10.119 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.119 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550119
06-12 23:09:10.119 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.119 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.119 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.119 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:10.119 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:10.119 D/NaApp   (10049): Template cache size: 27
06-12 23:09:10.119 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:10.260 D/NaApp   (10049): Avail: 2281MB | Total: 3558MB
06-12 23:09:10.260 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:10.260 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:10.260 D/NaApp   (10049): compareListImage started iteration 2 at 890.704077 ms
06-12 23:09:10.311 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:10.311 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:10.311 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:10.311 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:10.311 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:10.311 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:10.325 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:10.325 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:10.326 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:10.330 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:10.330 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:10.330 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:10.331 D/NaApp   (10049): grabScreenshot completed in 70.747269 ms
06-12 23:09:10.455 D/NaApp   (10049): Avail: 2268MB | Total: 3558MB
06-12 23:09:10.455 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:10.455 D/NaApp   (10049): templatePaths.forEach started at 1085.751769 ms
06-12 23:09:10.455 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:10.455 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1085.852576 ms
06-12 23:09:10.455 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550455
06-12 23:09:10.455 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.455 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.455 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550455
06-12 23:09:10.455 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.455 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.455 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.455 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1086.304269 ms
06-12 23:09:10.456 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:10.456 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:10.458 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:10.458 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:10.458 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:10.458 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.458 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.458 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:10.459 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.19274963438510895, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:10.459 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.19274963438510895 < threshold=0.9
06-12 23:09:10.459 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1089.709923 ms
06-12 23:09:10.510 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550509
06-12 23:09:10.510 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.510 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550509
06-12 23:09:10.510 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.510 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.510 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.510 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1140.531576 ms
06-12 23:09:10.510 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550510
06-12 23:09:10.510 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.510 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.510 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550510
06-12 23:09:10.510 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.510 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.510 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.510 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1140.874615 ms
06-12 23:09:10.510 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:10.510 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:10.512 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:10.512 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:10.512 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:10.512 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.512 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.513 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:10.513 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.18529285490512848, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:10.513 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.18529285490512848 >= threshold=0.9? false, brightness=242.43861607142856 < 100? false
06-12 23:09:10.513 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1143.960769 ms
06-12 23:09:10.564 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550564
06-12 23:09:10.564 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.564 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550564
06-12 23:09:10.564 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.564 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.564 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.564 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:10.564 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:10.564 D/NaApp   (10049): Template cache size: 27
06-12 23:09:10.564 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:10.653 D/NaApp   (10049): Avail: 2279MB | Total: 3558MB
06-12 23:09:10.653 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:10.653 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:10.653 D/NaApp   (10049): compareListImage started iteration 3 at 1283.526192 ms
06-12 23:09:10.703 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:10.704 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:10.704 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:10.704 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:10.704 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:10.704 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:10.709 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:10.709 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:10.710 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:10.715 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:10.715 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:10.716 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:10.717 D/NaApp   (10049): grabScreenshot completed in 64.220346 ms
06-12 23:09:10.788 D/NaApp   (10049): Avail: 2279MB | Total: 3558MB
06-12 23:09:10.788 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:10.788 D/NaApp   (10049): templatePaths.forEach started at 1419.262576 ms
06-12 23:09:10.788 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:10.789 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1419.347576 ms
06-12 23:09:10.789 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550789
06-12 23:09:10.789 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.789 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.789 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550789
06-12 23:09:10.789 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.789 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.789 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.789 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1419.770384 ms
06-12 23:09:10.789 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:10.789 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.789 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.789 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:10.790 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.05360827594995499, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:10.790 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.05360827594995499 < threshold=0.9
06-12 23:09:10.790 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1420.652115 ms
06-12 23:09:10.841 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550840
06-12 23:09:10.841 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.841 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550840
06-12 23:09:10.841 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.841 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.841 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.841 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1471.522961 ms
06-12 23:09:10.841 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744550841
06-12 23:09:10.841 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:10.841 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:10.841 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744550841
06-12 23:09:10.841 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:10.841 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:10.841 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.841 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1471.836769 ms
06-12 23:09:10.841 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:10.841 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:10.841 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:10.841 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:10.842 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.058639779686927795, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:10.842 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.058639779686927795 >= threshold=0.9? false, brightness=242.02901785714283 < 100? false
06-12 23:09:10.842 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1472.73823 ms
06-12 23:09:10.896 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744550895
06-12 23:09:10.896 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:10.896 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744550895
06-12 23:09:10.896 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:10.896 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:10.896 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:10.896 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:10.896 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:10.896 D/NaApp   (10049): Template cache size: 27
06-12 23:09:10.896 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:11.007 D/NaApp   (10049): Avail: 2279MB | Total: 3558MB
06-12 23:09:11.007 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:11.007 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:11.007 D/NaApp   (10049): compareListImage started iteration 4 at 1638.259115 ms
06-12 23:09:11.059 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:11.060 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:11.060 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:11.060 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:11.060 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:11.060 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:11.075 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:11.075 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:11.077 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:11.082 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:11.082 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:11.084 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:11.087 D/NaApp   (10049): grabScreenshot completed in 78.804039 ms
06-12 23:09:11.209 D/NaApp   (10049): Avail: 2281MB | Total: 3558MB
06-12 23:09:11.209 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:11.209 D/NaApp   (10049): templatePaths.forEach started at 1839.992807 ms
06-12 23:09:11.209 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:11.209 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1840.155999 ms
06-12 23:09:11.210 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744551209
06-12 23:09:11.210 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:11.210 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:11.210 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744551209
06-12 23:09:11.210 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:11.210 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:11.210 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.211 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1841.401153 ms
06-12 23:09:11.211 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:11.211 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:11.215 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:11.215 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:11.215 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:11.216 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:11.216 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:11.216 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:11.217 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.12253125756978989, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:11.217 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.12253125756978989 < threshold=0.9
06-12 23:09:11.217 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1847.878691 ms
06-12 23:09:11.268 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744551268
06-12 23:09:11.268 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:11.268 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744551268
06-12 23:09:11.268 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:11.268 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:11.268 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.268 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1899.302807 ms
06-12 23:09:11.269 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744551269
06-12 23:09:11.269 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:11.269 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:11.269 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744551269
06-12 23:09:11.269 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:11.269 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:11.269 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.269 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1900.196537 ms
06-12 23:09:11.269 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:11.269 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:11.274 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:11.274 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:11.274 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:11.275 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:11.275 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:11.275 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:11.277 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.11628830432891846, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:11.277 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.11628830432891846 >= threshold=0.9? false, brightness=241.93973214285714 < 100? false
06-12 23:09:11.277 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1907.74646 ms
06-12 23:09:11.328 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744551328
06-12 23:09:11.328 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:11.328 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744551328
06-12 23:09:11.328 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:11.328 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:11.328 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.328 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:11.328 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:11.329 D/NaApp   (10049): Template cache size: 27
06-12 23:09:11.329 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:11.453 D/NaApp   (10049): Avail: 2283MB | Total: 3558MB
06-12 23:09:11.453 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:11.453 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:11.453 D/NaApp   (10049): compareListImage started iteration 5 at 2083.517499 ms
06-12 23:09:11.504 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:11.505 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:11.505 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:11.505 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:11.505 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:11.505 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:11.513 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:11.514 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:11.515 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:11.538 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:11.538 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:11.540 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:11.550 D/NaApp   (10049): grabScreenshot completed in 96.705038 ms
06-12 23:09:11.685 D/NaApp   (10049): Avail: 2285MB | Total: 3558MB
06-12 23:09:11.685 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:11.685 D/NaApp   (10049): templatePaths.forEach started at 2315.452768 ms
06-12 23:09:11.685 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:11.685 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2315.620499 ms
06-12 23:09:11.685 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744551685
06-12 23:09:11.685 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:11.685 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:11.685 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744551685
06-12 23:09:11.685 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:11.685 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:11.685 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.686 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2316.554806 ms
06-12 23:09:11.686 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:11.686 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:11.686 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:11.687 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:11.688 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.0692853182554245, threshold=0.9, point=(18.0, 324.0)
06-12 23:09:11.688 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.0692853182554245 < threshold=0.9
06-12 23:09:11.688 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2318.518076 ms
06-12 23:09:11.739 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744551739
06-12 23:09:11.739 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:11.739 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744551739
06-12 23:09:11.739 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:11.739 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:11.739 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.739 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2369.903845 ms
06-12 23:09:11.739 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744551739
06-12 23:09:11.739 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:11.739 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:11.739 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744551739
06-12 23:09:11.739 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:11.739 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:11.739 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.740 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2370.514806 ms
06-12 23:09:11.740 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:11.740 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:11.740 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:11.740 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:11.741 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.06422469019889832, threshold=0.9, point=(19.0, 324.0)
06-12 23:09:11.741 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.06422469019889832 >= threshold=0.9? false, brightness=241.6361607142857 < 100? false
06-12 23:09:11.741 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2372.126191 ms
06-12 23:09:11.793 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744551792
06-12 23:09:11.793 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:11.793 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744551792
06-12 23:09:11.793 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:11.793 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:11.793 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:11.793 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:11.793 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:11.793 D/NaApp   (10049): Template cache size: 27
06-12 23:09:11.793 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:11.900 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:11.900 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:11.900 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:11.900 D/NaApp   (10049): compareListImage started iteration 6 at 2531.138075 ms
06-12 23:09:11.952 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:11.952 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:11.952 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:11.952 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:11.952 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:11.953 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:11.958 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:11.959 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:11.960 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:11.965 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:11.965 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:11.966 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:11.967 D/NaApp   (10049): grabScreenshot completed in 66.17877 ms
06-12 23:09:12.031 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:12.031 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:12.031 D/NaApp   (10049): templatePaths.forEach started at 2662.297691 ms
06-12 23:09:12.032 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:12.032 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2662.386575 ms
06-12 23:09:12.032 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552032
06-12 23:09:12.032 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.032 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.032 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552032
06-12 23:09:12.032 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.032 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.032 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.032 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2662.924614 ms
06-12 23:09:12.032 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:12.032 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:12.034 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:12.034 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:12.034 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:12.035 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.035 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.035 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:12.035 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.20433250069618225, threshold=0.9, point=(18.0, 324.0)
06-12 23:09:12.035 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.20433250069618225 < threshold=0.9
06-12 23:09:12.035 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2665.981998 ms
06-12 23:09:12.086 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552086
06-12 23:09:12.086 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.086 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552086
06-12 23:09:12.086 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.086 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.086 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.086 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2716.81346 ms
06-12 23:09:12.086 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552086
06-12 23:09:12.086 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.086 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.086 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552086
06-12 23:09:12.086 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.086 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.086 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.086 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2717.183652 ms
06-12 23:09:12.086 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:12.086 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:12.088 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:12.088 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:12.088 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:12.088 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.088 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.089 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:12.089 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.16699008643627167, threshold=0.9, point=(19.0, 324.0)
06-12 23:09:12.089 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.16699008643627167 >= threshold=0.9? false, brightness=241.17633928571428 < 100? false
06-12 23:09:12.089 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2719.962268 ms
06-12 23:09:12.140 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552140
06-12 23:09:12.140 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.140 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552140
06-12 23:09:12.140 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.140 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.140 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.140 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:12.140 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:12.140 D/NaApp   (10049): Template cache size: 27
06-12 23:09:12.140 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:12.222 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:12.222 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:12.222 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:12.222 D/NaApp   (10049): compareListImage started iteration 7 at 2853.09146 ms
06-12 23:09:12.274 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:12.274 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:12.274 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:12.275 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:12.275 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:12.275 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:12.276 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:12.276 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:12.278 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:12.287 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:12.287 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:12.288 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:12.290 D/NaApp   (10049): grabScreenshot completed in 67.225576 ms
06-12 23:09:12.408 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:12.408 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:12.408 D/NaApp   (10049): templatePaths.forEach started at 3039.295114 ms
06-12 23:09:12.409 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:12.409 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3039.460575 ms
06-12 23:09:12.409 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552409
06-12 23:09:12.409 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.409 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.409 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552409
06-12 23:09:12.409 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.409 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.409 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.410 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3040.488344 ms
06-12 23:09:12.410 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:12.410 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.410 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.410 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:12.411 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.31186217069625854, threshold=0.9, point=(18.0, 324.0)
06-12 23:09:12.412 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.31186217069625854 < threshold=0.9
06-12 23:09:12.412 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3042.435806 ms
06-12 23:09:12.463 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552463
06-12 23:09:12.463 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.463 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552463
06-12 23:09:12.463 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.463 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.463 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.463 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3094.049498 ms
06-12 23:09:12.463 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552463
06-12 23:09:12.463 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.463 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.463 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552463
06-12 23:09:12.464 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.464 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.464 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.465 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3095.34319 ms
06-12 23:09:12.465 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:12.465 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.465 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.466 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:12.467 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.2485952079296112, threshold=0.9, point=(19.0, 324.0)
06-12 23:09:12.467 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.2485952079296112 >= threshold=0.9? false, brightness=240.85267857142856 < 100? false
06-12 23:09:12.467 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3097.904844 ms
06-12 23:09:12.518 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552518
06-12 23:09:12.518 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.518 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552518
06-12 23:09:12.518 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.518 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.519 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.519 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:12.519 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:12.519 D/NaApp   (10049): Template cache size: 27
06-12 23:09:12.519 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:12.642 D/NaApp   (10049): Avail: 2285MB | Total: 3558MB
06-12 23:09:12.642 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:12.642 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:12.642 D/NaApp   (10049): compareListImage started iteration 8 at 3272.799075 ms
06-12 23:09:12.693 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:12.694 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:12.694 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:12.695 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:12.695 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:12.695 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:12.708 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:12.709 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:12.711 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:12.717 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:12.717 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:12.718 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:12.720 D/NaApp   (10049): grabScreenshot completed in 77.701423 ms
06-12 23:09:12.842 D/NaApp   (10049): Avail: 2285MB | Total: 3558MB
06-12 23:09:12.842 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:12.843 D/NaApp   (10049): templatePaths.forEach started at 3473.341344 ms
06-12 23:09:12.843 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:12.843 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3473.503959 ms
06-12 23:09:12.843 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552843
06-12 23:09:12.843 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.843 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.843 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552843
06-12 23:09:12.843 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.843 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.843 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.844 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3474.59619 ms
06-12 23:09:12.844 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:12.844 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:12.848 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:12.848 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:12.848 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:12.849 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.849 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.849 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:12.850 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.4625280499458313, threshold=0.9, point=(20.0, 322.0)
06-12 23:09:12.850 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.4625280499458313 < threshold=0.9
06-12 23:09:12.850 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3480.881036 ms
06-12 23:09:12.901 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552901
06-12 23:09:12.901 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.901 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552901
06-12 23:09:12.901 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.902 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.902 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.902 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3532.546921 ms
06-12 23:09:12.902 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744552902
06-12 23:09:12.902 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:12.902 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:12.902 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744552902
06-12 23:09:12.902 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:12.902 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:12.902 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.903 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3533.537767 ms
06-12 23:09:12.903 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:12.903 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:12.908 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:12.908 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:12.909 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:12.909 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:12.909 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:12.910 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:12.911 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.43786153197288513, threshold=0.9, point=(19.0, 322.0)
06-12 23:09:12.911 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.43786153197288513 >= threshold=0.9? false, brightness=240.78125 < 100? false
06-12 23:09:12.911 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3542.124921 ms
06-12 23:09:12.962 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744552962
06-12 23:09:12.963 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:12.963 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744552962
06-12 23:09:12.963 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:12.963 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:12.963 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:12.963 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:12.963 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:12.963 D/NaApp   (10049): Template cache size: 27
06-12 23:09:12.963 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:13.085 D/NaApp   (10049): Avail: 2285MB | Total: 3558MB
06-12 23:09:13.085 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:13.085 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:13.085 D/NaApp   (10049): compareListImage started iteration 9 at 3715.801228 ms
06-12 23:09:13.137 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:13.138 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:13.138 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:13.138 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:13.138 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:13.138 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:13.143 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:13.144 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:13.146 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:13.164 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:13.165 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:13.166 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:13.168 D/NaApp   (10049): grabScreenshot completed in 82.790538 ms
06-12 23:09:13.275 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:13.275 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:13.275 D/NaApp   (10049): templatePaths.forEach started at 3905.524805 ms
06-12 23:09:13.275 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:13.275 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3905.679267 ms
06-12 23:09:13.275 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744553275
06-12 23:09:13.275 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:13.275 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:13.275 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744553275
06-12 23:09:13.275 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:13.275 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:13.275 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.276 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3906.365805 ms
06-12 23:09:13.276 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:13.276 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:13.276 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:13.276 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:13.277 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.4723779559135437, threshold=0.9, point=(28.0, 320.0)
06-12 23:09:13.277 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.4723779559135437 < threshold=0.9
06-12 23:09:13.277 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3908.090844 ms
06-12 23:09:13.329 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744553328
06-12 23:09:13.329 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:13.329 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744553328
06-12 23:09:13.329 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:13.329 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:13.329 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.329 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3959.682536 ms
06-12 23:09:13.329 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744553329
06-12 23:09:13.329 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:13.329 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:13.329 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744553329
06-12 23:09:13.329 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:13.329 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:13.329 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.330 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3960.960459 ms
06-12 23:09:13.330 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:13.331 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:13.331 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:13.331 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:13.332 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.45343512296676636, threshold=0.9, point=(29.0, 320.0)
06-12 23:09:13.332 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.45343512296676636 >= threshold=0.9? false, brightness=241.15290178571428 < 100? false
06-12 23:09:13.332 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3963.242497 ms
06-12 23:09:13.385 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744553385
06-12 23:09:13.385 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:13.386 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744553385
06-12 23:09:13.387 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:13.387 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:13.387 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.387 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:13.387 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:13.387 D/NaApp   (10049): Template cache size: 27
06-12 23:09:13.387 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:13.507 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:13.507 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:13.507 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:13.507 D/NaApp   (10049): compareListImage started iteration 10 at 4138.090767 ms
06-12 23:09:13.559 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:13.560 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:13.560 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:13.560 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:13.561 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:13.561 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:13.577 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:13.578 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:13.580 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:13.587 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:13.587 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:13.589 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:13.591 D/NaApp   (10049): grabScreenshot completed in 82.854346 ms
06-12 23:09:13.720 D/NaApp   (10049): Avail: 2285MB | Total: 3558MB
06-12 23:09:13.720 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:13.720 D/NaApp   (10049): templatePaths.forEach started at 4350.899882 ms
06-12 23:09:13.720 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:13.720 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4351.102382 ms
06-12 23:09:13.720 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744553720
06-12 23:09:13.721 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:13.721 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:13.721 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744553720
06-12 23:09:13.721 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:13.721 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:13.721 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.721 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4352.083113 ms
06-12 23:09:13.721 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:13.721 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:13.726 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:13.726 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:13.726 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:13.727 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:13.727 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:13.727 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:13.728 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.4706468880176544, threshold=0.9, point=(42.0, 320.0)
06-12 23:09:13.728 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.4706468880176544 < threshold=0.9
06-12 23:09:13.728 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4359.01192 ms
06-12 23:09:13.780 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744553779
06-12 23:09:13.780 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:13.780 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744553779
06-12 23:09:13.780 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:13.780 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:13.780 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.780 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4410.659843 ms
06-12 23:09:13.780 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744553780
06-12 23:09:13.780 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:13.780 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:13.780 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744553780
06-12 23:09:13.780 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:13.780 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:13.780 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.781 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4411.470997 ms
06-12 23:09:13.781 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:13.781 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:13.786 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:13.786 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:13.786 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:13.787 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:13.787 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:13.787 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:13.789 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.45379433035850525, threshold=0.9, point=(41.0, 320.0)
06-12 23:09:13.789 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.45379433035850525 >= threshold=0.9? false, brightness=241.59486607142856 < 100? false
06-12 23:09:13.789 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4419.756189 ms
06-12 23:09:13.840 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744553840
06-12 23:09:13.840 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:13.840 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744553840
06-12 23:09:13.840 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:13.840 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:13.840 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:13.840 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:13.840 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:13.840 D/NaApp   (10049): Template cache size: 27
06-12 23:09:13.840 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:13.962 D/NaApp   (10049): Avail: 2284MB | Total: 3558MB
06-12 23:09:13.962 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:13.962 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:13.962 D/NaApp   (10049): compareListImage started iteration 11 at 4593.186766 ms
06-12 23:09:14.014 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:14.014 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:14.014 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:14.015 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:14.015 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:14.015 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:14.027 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:14.027 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:14.029 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:14.043 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:14.043 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:14.045 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:14.047 D/NaApp   (10049): grabScreenshot completed in 83.811154 ms
06-12 23:09:14.184 D/NaApp   (10049): Avail: 2336MB | Total: 3558MB
06-12 23:09:14.184 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:14.184 D/NaApp   (10049): templatePaths.forEach started at 4815.103766 ms
06-12 23:09:14.184 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:14.184 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4815.266305 ms
06-12 23:09:14.185 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554184
06-12 23:09:14.185 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.185 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.185 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554184
06-12 23:09:14.185 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.185 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.185 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.185 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4816.291381 ms
06-12 23:09:14.186 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:14.186 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.186 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.186 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:14.187 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.5060420632362366, threshold=0.9, point=(24.0, 310.0)
06-12 23:09:14.187 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.5060420632362366 < threshold=0.9
06-12 23:09:14.187 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4818.170689 ms
06-12 23:09:14.241 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744554241
06-12 23:09:14.241 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:14.241 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744554241
06-12 23:09:14.241 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:14.241 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:14.241 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.241 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4871.985728 ms
06-12 23:09:14.241 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554241
06-12 23:09:14.241 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.241 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.241 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554241
06-12 23:09:14.241 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.242 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.242 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.242 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4873.085958 ms
06-12 23:09:14.242 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:14.243 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.243 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.243 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:14.245 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.4750804603099823, threshold=0.9, point=(25.0, 310.0)
06-12 23:09:14.245 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.4750804603099823 >= threshold=0.9? false, brightness=242.74888392857142 < 100? false
06-12 23:09:14.245 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4875.691689 ms
06-12 23:09:14.298 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744554298
06-12 23:09:14.298 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:14.298 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744554298
06-12 23:09:14.298 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:14.298 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:14.298 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.298 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:14.298 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:14.298 D/NaApp   (10049): Template cache size: 27
06-12 23:09:14.298 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:14.415 D/NaApp   (10049): Avail: 2335MB | Total: 3558MB
06-12 23:09:14.416 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:14.416 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:14.416 D/NaApp   (10049): compareListImage started iteration 12 at 5046.43942 ms
06-12 23:09:14.467 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:14.468 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:14.468 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:14.468 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:14.468 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:14.468 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:14.475 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:14.476 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:14.477 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:14.483 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:14.483 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:14.485 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:14.486 D/NaApp   (10049): grabScreenshot completed in 69.988808 ms
06-12 23:09:14.578 D/NaApp   (10049): Avail: 2335MB | Total: 3558MB
06-12 23:09:14.578 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:14.578 D/NaApp   (10049): templatePaths.forEach started at 5208.732227 ms
06-12 23:09:14.578 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:14.578 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5208.872881 ms
06-12 23:09:14.578 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554578
06-12 23:09:14.578 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.578 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.578 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554578
06-12 23:09:14.578 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.578 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.578 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.579 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5209.670458 ms
06-12 23:09:14.579 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:14.579 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:14.582 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:14.582 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:14.583 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:14.583 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.583 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.583 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:14.584 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.4998991787433624, threshold=0.9, point=(36.0, 310.0)
06-12 23:09:14.584 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.4998991787433624 < threshold=0.9
06-12 23:09:14.584 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5214.879304 ms
06-12 23:09:14.635 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744554635
06-12 23:09:14.635 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:14.635 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744554635
06-12 23:09:14.635 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:14.635 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:14.635 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.635 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5266.030766 ms
06-12 23:09:14.635 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554635
06-12 23:09:14.635 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.635 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.635 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554635
06-12 23:09:14.635 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.635 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.635 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.636 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5266.695689 ms
06-12 23:09:14.636 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:14.636 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:14.639 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:14.639 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:14.639 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:14.639 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.640 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.640 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:14.640 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.46882423758506775, threshold=0.9, point=(37.0, 310.0)
06-12 23:09:14.641 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.46882423758506775 >= threshold=0.9? false, brightness=244.7064732142857 < 100? false
06-12 23:09:14.641 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5271.505304 ms
06-12 23:09:14.692 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744554692
06-12 23:09:14.692 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:14.692 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744554692
06-12 23:09:14.692 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:14.692 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:14.692 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.692 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:14.692 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:14.692 D/NaApp   (10049): Template cache size: 27
06-12 23:09:14.692 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:14.768 D/NaApp   (10049): Avail: 2335MB | Total: 3558MB
06-12 23:09:14.768 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:14.768 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:14.768 D/NaApp   (10049): compareListImage started iteration 13 at 5398.484227 ms
06-12 23:09:14.818 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:14.819 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:14.819 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:14.819 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:14.819 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:14.819 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:14.825 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:14.825 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:14.827 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:14.833 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:14.833 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:14.834 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:14.837 D/NaApp   (10049): grabScreenshot completed in 69.053384 ms
06-12 23:09:14.914 D/NaApp   (10049): Avail: 2335MB | Total: 3558MB
06-12 23:09:14.914 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:09:14.914 D/NaApp   (10049): templatePaths.forEach started at 5545.079073 ms
06-12 23:09:14.914 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:14.914 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5545.162266 ms
06-12 23:09:14.914 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554914
06-12 23:09:14.914 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.914 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.914 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554914
06-12 23:09:14.914 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.914 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.914 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.915 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5545.510612 ms
06-12 23:09:14.915 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:14.915 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.915 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.915 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:14.915 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.4727652370929718, threshold=0.9, point=(44.0, 310.0)
06-12 23:09:14.915 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.4727652370929718 < threshold=0.9
06-12 23:09:14.915 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5546.323843 ms
06-12 23:09:14.969 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744554969
06-12 23:09:14.969 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:14.969 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744554969
06-12 23:09:14.969 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:14.969 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:14.969 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.969 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5600.013689 ms
06-12 23:09:14.969 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744554969
06-12 23:09:14.969 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:14.969 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:14.969 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744554969
06-12 23:09:14.969 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:14.970 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:14.970 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:14.970 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5600.91715 ms
06-12 23:09:14.970 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:14.971 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:14.971 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:14.971 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:14.973 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.4289824962615967, threshold=0.9, point=(43.0, 310.0)
06-12 23:09:14.973 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.4289824962615967 >= threshold=0.9? false, brightness=246.00446428571428 < 100? false
06-12 23:09:14.973 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5603.687766 ms
06-12 23:09:15.029 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744555028
06-12 23:09:15.029 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:15.029 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744555028
06-12 23:09:15.029 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:15.029 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:15.029 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:15.029 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:15.029 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:15.029 D/NaApp   (10049): Template cache size: 27
06-12 23:09:15.029 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:15.029 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:09:15.029 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:15.029 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:15.029 D/NaApp   (10049): Template cache size: 27
06-12 23:09:15.029 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:09:15.029 D/NaApp   (10049): compareImagesLong result at 5660.226612 ms: FAILED
06-12 23:09:15.030 D/NaApp   (10049): checkLoadGame: compareImagesLong completed in 5660.394842 ms
06-12 23:09:15.030 D/NaApp   (10049): Check load game: (FAILED, null)
06-12 23:09:15.030 E/NaApp   (10049): Check failed: (FAILED, null)
06-12 23:09:15.030 E/NaApp   (10049): Fail Farm 3
06-12 23:09:15.030 D/NaApp   (10049): Automation cycle completed
06-12 23:09:15.214 D/NaApp   (10049): Auto index: 2
06-12 23:09:15.304 D/NaApp   (10049): Avail: 2336MB | Total: 3558MB
06-12 23:09:15.304 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:09:15.668 D/NaApp   (10049): CPU Usage: 29%
06-12 23:09:15.673 D/NaApp   (10049): Folder already exists: /storage/emulated/0/Dragon/Farm
06-12 23:09:15.678 W/NaApp   (10049): Go Home
06-12 23:09:16.547 D/NaApp   (10049): Command success: am force-stop com.supercell.hayday
06-12 23:09:16.674 D/NaApp   (10049): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:09:16.740 D/NaApp   (10049): Command success: service call SurfaceFlinger 1008 i32 0
06-12 23:09:16.840 D/NaApp   (10049): Command success: sync
06-12 23:09:17.155 D/NaApp   (10049): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 23:09:17.340 D/NaApp   (10049): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 23:09:17.399 D/NaApp   (10049): Command success: pm trim-caches 100M
06-12 23:09:17.451 D/NaApp   (10049): Command success: am kill-all
06-12 23:09:17.451 D/NaApp   (10049): Killed com.supercell.hayday
06-12 23:09:18.925 D/NaApp   (10049): Successfully applied Farm #3
06-12 23:09:27.465 D/NaApp   (10049): Open Farm: 3 / 10
06-12 23:09:27.497 D/NaApp   (10049): Farm opened successfully
06-12 23:09:27.502 D/NaApp   (10049): Checking game load
06-12 23:09:27.502 D/NaApp   (10049): checkLoadGame: compareImagesLong started at 1029820.747186 ms
06-12 23:09:27.679 D/NaApp   (10049): Avail: 2345MB | Total: 3558MB
06-12 23:09:27.679 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:27.679 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:27.679 D/NaApp   (10049): compareListImage started iteration 0 at 176.304462 ms
06-12 23:09:27.730 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:27.730 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:27.730 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:27.730 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:27.731 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:27.731 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:27.751 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:27.751 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:27.754 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:27.763 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:27.763 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:27.764 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:27.765 D/NaApp   (10049): grabScreenshot completed in 86.278923 ms
06-12 23:09:27.886 D/NaApp   (10049): Avail: 2343MB | Total: 3558MB
06-12 23:09:27.886 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:27.886 D/NaApp   (10049): templatePaths.forEach started at 383.762731 ms
06-12 23:09:27.886 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:27.886 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 383.852846 ms
06-12 23:09:27.886 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744567886
06-12 23:09:27.886 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:27.886 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:27.886 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744567886
06-12 23:09:27.886 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:27.886 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:27.886 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:27.887 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 384.322115 ms
06-12 23:09:27.887 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:27.887 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:27.891 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:27.891 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:27.891 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:27.891 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:27.891 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:27.891 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:27.892 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.18292038142681122, threshold=0.9, point=(38.0, 324.0)
06-12 23:09:27.892 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.18292038142681122 < threshold=0.9
06-12 23:09:27.892 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 389.594423 ms
06-12 23:09:27.943 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744567943
06-12 23:09:27.943 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:27.943 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744567943
06-12 23:09:27.943 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:27.943 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:27.943 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:27.943 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 440.843769 ms
06-12 23:09:27.943 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744567943
06-12 23:09:27.943 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:27.943 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:27.943 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744567943
06-12 23:09:27.943 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:27.943 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:27.943 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:27.943 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 441.142577 ms
06-12 23:09:27.944 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:27.944 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:27.946 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:27.946 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:27.946 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:27.946 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:27.946 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:27.946 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:27.947 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.199844628572464, threshold=0.9, point=(39.0, 324.0)
06-12 23:09:27.947 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.199844628572464 >= threshold=0.9? false, brightness=244.84598214285714 < 100? false
06-12 23:09:27.947 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 444.424961 ms
06-12 23:09:27.998 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744567998
06-12 23:09:27.998 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:27.998 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744567998
06-12 23:09:27.998 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:27.998 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:27.998 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:27.998 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:27.998 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:27.998 D/NaApp   (10049): Template cache size: 27
06-12 23:09:27.998 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:28.125 D/NaApp   (10049): Avail: 2340MB | Total: 3558MB
06-12 23:09:28.125 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:28.125 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:28.125 D/NaApp   (10049): compareListImage started iteration 1 at 622.495307 ms
06-12 23:09:28.176 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:28.177 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:28.177 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:28.178 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:28.178 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:28.178 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:28.187 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:28.187 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:28.191 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:28.203 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:28.203 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:28.207 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:28.210 D/NaApp   (10049): grabScreenshot completed in 84.661384 ms
06-12 23:09:28.321 D/NaApp   (10049): Avail: 2339MB | Total: 3558MB
06-12 23:09:28.321 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:28.321 D/NaApp   (10049): templatePaths.forEach started at 818.457423 ms
06-12 23:09:28.321 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:28.321 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 818.635269 ms
06-12 23:09:28.321 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744568321
06-12 23:09:28.321 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:28.321 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:28.321 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744568321
06-12 23:09:28.321 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:28.321 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:28.321 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.322 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 819.571307 ms
06-12 23:09:28.322 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:28.322 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:28.322 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:28.323 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:28.324 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.1386222541332245, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:28.324 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.1386222541332245 < threshold=0.9
06-12 23:09:28.324 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 821.440346 ms
06-12 23:09:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744568374
06-12 23:09:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:28.375 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744568374
06-12 23:09:28.375 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.375 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 872.498923 ms
06-12 23:09:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744568375
06-12 23:09:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:28.375 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:28.375 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744568375
06-12 23:09:28.375 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:28.375 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.375 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 873.107846 ms
06-12 23:09:28.376 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:28.376 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:28.376 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:28.376 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:28.377 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.13774307072162628, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:28.377 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.13774307072162628 >= threshold=0.9? false, brightness=244.375 < 100? false
06-12 23:09:28.377 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 874.712615 ms
06-12 23:09:28.429 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744568428
06-12 23:09:28.429 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:28.429 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744568428
06-12 23:09:28.429 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:28.429 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:28.429 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.429 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:28.429 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:28.429 D/NaApp   (10049): Template cache size: 27
06-12 23:09:28.429 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:28.546 D/NaApp   (10049): Avail: 2335MB | Total: 3558MB
06-12 23:09:28.546 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:28.546 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:28.546 D/NaApp   (10049): compareListImage started iteration 2 at 1044.023692 ms
06-12 23:09:28.597 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:28.598 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:28.599 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:28.600 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:28.600 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:28.600 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:28.756 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:28.756 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:28.758 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:28.773 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:28.773 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:28.778 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:28.780 D/NaApp   (10049): grabScreenshot completed in 233.070808 ms
06-12 23:09:28.883 D/NaApp   (10049): Avail: 2292MB | Total: 3558MB
06-12 23:09:28.883 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:28.883 D/NaApp   (10049): templatePaths.forEach started at 1380.274961 ms
06-12 23:09:28.883 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:28.883 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1380.414038 ms
06-12 23:09:28.883 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744568883
06-12 23:09:28.883 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:28.883 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:28.883 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744568883
06-12 23:09:28.883 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:28.883 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:28.883 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.883 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1381.094038 ms
06-12 23:09:28.884 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:28.884 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:28.889 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:28.889 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:28.889 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:28.889 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:28.889 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:28.889 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:28.890 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.10357718169689178, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:28.890 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.10357718169689178 < threshold=0.9
06-12 23:09:28.890 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1387.876884 ms
06-12 23:09:28.941 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744568941
06-12 23:09:28.941 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:28.941 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744568941
06-12 23:09:28.941 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:28.941 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:28.941 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.941 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1438.908076 ms
06-12 23:09:28.941 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744568941
06-12 23:09:28.941 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:28.941 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:28.941 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744568941
06-12 23:09:28.941 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:28.941 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:28.941 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.942 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1439.756461 ms
06-12 23:09:28.942 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:28.942 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:28.945 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:28.945 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:28.945 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:28.946 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:28.946 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:28.946 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:28.947 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.11924973130226135, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:28.947 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.11924973130226135 >= threshold=0.9? false, brightness=244.65848214285714 < 100? false
06-12 23:09:28.947 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1444.397384 ms
06-12 23:09:28.998 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744568997
06-12 23:09:28.998 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:28.998 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744568997
06-12 23:09:28.998 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:28.998 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:28.998 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:28.998 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:28.998 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:28.998 D/NaApp   (10049): Template cache size: 27
06-12 23:09:28.998 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:29.106 D/NaApp   (10049): Avail: 2250MB | Total: 3558MB
06-12 23:09:29.106 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:29.106 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:29.106 D/NaApp   (10049): compareListImage started iteration 3 at 1603.866038 ms
06-12 23:09:29.157 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:29.158 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:29.158 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:29.158 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:29.158 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:29.158 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:29.177 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:29.177 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:29.179 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:29.182 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:29.182 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:29.184 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:29.185 D/NaApp   (10049): grabScreenshot completed in 78.712654 ms
06-12 23:09:29.266 D/NaApp   (10049): Avail: 2264MB | Total: 3558MB
06-12 23:09:29.266 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:29.266 D/NaApp   (10049): templatePaths.forEach started at 1763.916037 ms
06-12 23:09:29.266 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:29.266 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 1764.005537 ms
06-12 23:09:29.266 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569266
06-12 23:09:29.266 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.266 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.266 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569266
06-12 23:09:29.266 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.267 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.267 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.267 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 1764.472537 ms
06-12 23:09:29.267 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:29.267 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.267 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.267 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:29.268 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.016559233888983727, threshold=0.9, point=(44.0, 324.0)
06-12 23:09:29.268 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.016559233888983727 < threshold=0.9
06-12 23:09:29.268 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 1765.409537 ms
06-12 23:09:29.318 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569318
06-12 23:09:29.319 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.319 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569318
06-12 23:09:29.319 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.319 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.319 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.319 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 1816.243999 ms
06-12 23:09:29.319 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569319
06-12 23:09:29.319 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.319 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.319 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569319
06-12 23:09:29.319 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.319 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.319 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.319 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 1816.612268 ms
06-12 23:09:29.319 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:29.319 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.319 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.319 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:29.320 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.02927098050713539, threshold=0.9, point=(43.0, 324.0)
06-12 23:09:29.320 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.02927098050713539 >= threshold=0.9? false, brightness=245.54129464285714 < 100? false
06-12 23:09:29.320 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 1817.41646 ms
06-12 23:09:29.373 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569373
06-12 23:09:29.373 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.373 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569373
06-12 23:09:29.373 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.373 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.373 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.373 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:29.373 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:29.373 D/NaApp   (10049): Template cache size: 27
06-12 23:09:29.373 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:29.461 D/NaApp   (10049): Avail: 2255MB | Total: 3558MB
06-12 23:09:29.461 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:29.461 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:29.461 D/NaApp   (10049): compareListImage started iteration 4 at 1958.587653 ms
06-12 23:09:29.512 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:29.513 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:29.513 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:29.513 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:29.514 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:29.514 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:29.524 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:29.524 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:29.525 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:29.532 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:29.535 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:29.536 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:29.536 D/NaApp   (10049): grabScreenshot completed in 74.9835 ms
06-12 23:09:29.604 D/NaApp   (10049): Avail: 2247MB | Total: 3558MB
06-12 23:09:29.605 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:29.605 D/NaApp   (10049): templatePaths.forEach started at 2102.18746 ms
06-12 23:09:29.605 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:29.605 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2102.275806 ms
06-12 23:09:29.605 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569605
06-12 23:09:29.605 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.605 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.605 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569605
06-12 23:09:29.605 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.605 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.605 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.605 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2102.684883 ms
06-12 23:09:29.606 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:29.606 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:29.609 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:29.609 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:29.609 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:29.609 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.609 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.609 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:29.610 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.12171132862567902, threshold=0.9, point=(44.0, 320.0)
06-12 23:09:29.610 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.12171132862567902 < threshold=0.9
06-12 23:09:29.610 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2107.478922 ms
06-12 23:09:29.660 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569660
06-12 23:09:29.660 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.661 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569660
06-12 23:09:29.661 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.661 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.661 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.661 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2158.225653 ms
06-12 23:09:29.661 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569661
06-12 23:09:29.661 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.661 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.661 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569661
06-12 23:09:29.661 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.661 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.661 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.661 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2158.698153 ms
06-12 23:09:29.661 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:29.661 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:29.663 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:29.663 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:29.663 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:29.663 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.663 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.664 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:29.664 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.1115247905254364, threshold=0.9, point=(43.0, 320.0)
06-12 23:09:29.664 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.1115247905254364 >= threshold=0.9? false, brightness=246.43861607142856 < 100? false
06-12 23:09:29.664 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2161.727922 ms
06-12 23:09:29.715 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569715
06-12 23:09:29.715 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.715 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569715
06-12 23:09:29.715 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.715 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.715 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.715 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:29.715 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:29.715 D/NaApp   (10049): Template cache size: 27
06-12 23:09:29.715 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:29.779 D/NaApp   (10049): Avail: 2260MB | Total: 3558MB
06-12 23:09:29.779 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:29.779 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:29.779 D/NaApp   (10049): compareListImage started iteration 5 at 2276.772114 ms
06-12 23:09:29.830 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:29.831 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:29.831 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:29.831 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:29.831 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:29.831 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:29.840 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:29.840 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:29.841 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:29.845 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:29.845 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:29.846 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:29.847 D/NaApp   (10049): grabScreenshot completed in 67.5255 ms
06-12 23:09:29.885 D/NaApp   (10049): Avail: 2260MB | Total: 3558MB
06-12 23:09:29.885 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:29.885 D/NaApp   (10049): templatePaths.forEach started at 2382.317306 ms
06-12 23:09:29.885 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:29.885 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2382.39896 ms
06-12 23:09:29.885 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569885
06-12 23:09:29.885 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.885 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.885 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569885
06-12 23:09:29.885 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.885 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.885 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.885 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2382.812883 ms
06-12 23:09:29.885 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:29.885 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.885 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.886 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:29.886 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.0810975730419159, threshold=0.9, point=(18.0, 310.0)
06-12 23:09:29.886 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.0810975730419159 < threshold=0.9
06-12 23:09:29.886 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2383.719383 ms
06-12 23:09:29.937 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569937
06-12 23:09:29.937 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.937 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569937
06-12 23:09:29.937 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.937 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.937 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.937 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2434.585306 ms
06-12 23:09:29.937 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744569937
06-12 23:09:29.937 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:29.937 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:29.937 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744569937
06-12 23:09:29.937 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:29.937 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:29.937 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.937 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2434.852075 ms
06-12 23:09:29.937 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:29.937 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:29.937 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:29.937 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:29.938 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.11017091572284698, threshold=0.9, point=(33.0, 310.0)
06-12 23:09:29.938 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.11017091572284698 >= threshold=0.9? false, brightness=247.05915178571428 < 100? false
06-12 23:09:29.938 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2435.559383 ms
06-12 23:09:29.989 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744569989
06-12 23:09:29.989 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:29.989 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744569989
06-12 23:09:29.989 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:29.989 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:29.989 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:29.990 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:29.990 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:29.990 D/NaApp   (10049): Template cache size: 27
06-12 23:09:29.990 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:30.109 D/NaApp   (10049): Avail: 2260MB | Total: 3558MB
06-12 23:09:30.109 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:30.109 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:30.109 D/NaApp   (10049): compareListImage started iteration 6 at 2606.471421 ms
06-12 23:09:30.160 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:30.161 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:30.161 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:30.161 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:30.161 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:30.161 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:30.170 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:30.170 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:30.173 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:30.177 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:30.177 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:30.178 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:30.182 D/NaApp   (10049): grabScreenshot completed in 72.31877 ms
06-12 23:09:30.296 D/NaApp   (10049): Avail: 2260MB | Total: 3558MB
06-12 23:09:30.296 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:30.296 D/NaApp   (10049): templatePaths.forEach started at 2793.854691 ms
06-12 23:09:30.296 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:30.296 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 2794.032498 ms
06-12 23:09:30.297 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744570296
06-12 23:09:30.297 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:30.297 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:30.297 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744570296
06-12 23:09:30.297 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:30.297 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:30.297 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.297 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 2794.776229 ms
06-12 23:09:30.297 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:30.297 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:30.302 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:30.302 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:30.302 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:30.302 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:30.302 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:30.303 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:30.304 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=-0.0010802618926391006, threshold=0.9, point=(34.0, 310.0)
06-12 23:09:30.304 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=-0.0010802618926391006 < threshold=0.9
06-12 23:09:30.304 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 2801.375652 ms
06-12 23:09:30.355 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744570355
06-12 23:09:30.355 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:30.355 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744570355
06-12 23:09:30.355 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:30.355 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:30.355 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.355 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 2853.025421 ms
06-12 23:09:30.356 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744570355
06-12 23:09:30.356 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:30.356 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:30.356 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744570355
06-12 23:09:30.356 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:30.356 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:30.356 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.357 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 2854.26696 ms
06-12 23:09:30.357 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:30.357 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:30.361 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:30.362 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:30.362 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:30.362 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:30.362 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:30.363 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:30.364 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=-0.028004314750432968, threshold=0.9, point=(33.0, 310.0)
06-12 23:09:30.364 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=-0.028004314750432968 >= threshold=0.9? false, brightness=247.31473214285714 < 100? false
06-12 23:09:30.364 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 2861.926883 ms
06-12 23:09:30.417 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744570416
06-12 23:09:30.417 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:30.417 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744570416
06-12 23:09:30.417 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:30.417 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:30.417 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.417 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:30.417 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:30.417 D/NaApp   (10049): Template cache size: 27
06-12 23:09:30.417 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:30.548 D/NaApp   (10049): Avail: 2262MB | Total: 3558MB
06-12 23:09:30.548 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:30.548 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:30.549 D/NaApp   (10049): compareListImage started iteration 7 at 3046.15619 ms
06-12 23:09:30.600 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:30.601 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:30.601 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:30.601 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:30.602 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:30.602 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:30.605 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:30.609 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:30.613 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:30.619 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:30.627 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:30.630 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:30.632 D/NaApp   (10049): grabScreenshot completed in 83.319769 ms
06-12 23:09:30.760 D/NaApp   (10049): Avail: 2266MB | Total: 3558MB
06-12 23:09:30.760 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:30.760 D/NaApp   (10049): templatePaths.forEach started at 3257.78919 ms
06-12 23:09:30.760 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:30.760 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3257.947921 ms
06-12 23:09:30.761 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744570760
06-12 23:09:30.761 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:30.761 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:30.761 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744570760
06-12 23:09:30.761 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:30.761 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:30.761 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.762 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3259.216613 ms
06-12 23:09:30.762 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:30.762 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:30.762 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:30.762 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:30.763 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.13991792500019073, threshold=0.9, point=(40.0, 310.0)
06-12 23:09:30.764 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.13991792500019073 < threshold=0.9
06-12 23:09:30.764 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3261.188267 ms
06-12 23:09:30.815 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744570815
06-12 23:09:30.815 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:30.815 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744570815
06-12 23:09:30.815 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:30.815 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:30.815 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.815 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3312.792075 ms
06-12 23:09:30.815 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744570815
06-12 23:09:30.815 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:30.815 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:30.815 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744570815
06-12 23:09:30.815 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:30.816 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:30.816 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.817 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3314.647459 ms
06-12 23:09:30.817 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:30.818 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:30.818 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:30.818 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:30.819 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.11719835549592972, threshold=0.9, point=(39.0, 310.0)
06-12 23:09:30.819 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.11719835549592972 >= threshold=0.9? false, brightness=247.84263392857142 < 100? false
06-12 23:09:30.819 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3317.09819 ms
06-12 23:09:30.872 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744570872
06-12 23:09:30.872 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:30.872 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744570872
06-12 23:09:30.872 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:30.872 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:30.872 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:30.872 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:30.872 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:30.872 D/NaApp   (10049): Template cache size: 27
06-12 23:09:30.872 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:30.972 D/NaApp   (10049): Avail: 2264MB | Total: 3558MB
06-12 23:09:30.972 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:30.972 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:30.972 D/NaApp   (10049): compareListImage started iteration 8 at 3469.951382 ms
06-12 23:09:31.024 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:31.025 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:31.025 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:31.025 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:31.025 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:31.025 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:31.071 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:31.075 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:31.080 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:31.086 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:31.086 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:31.088 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:31.090 D/NaApp   (10049): grabScreenshot completed in 116.874961 ms
06-12 23:09:31.192 D/NaApp   (10049): Avail: 2257MB | Total: 3558MB
06-12 23:09:31.192 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:31.192 D/NaApp   (10049): templatePaths.forEach started at 3689.446844 ms
06-12 23:09:31.192 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:31.192 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 3689.647536 ms
06-12 23:09:31.192 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744571192
06-12 23:09:31.192 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:31.192 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:31.192 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744571192
06-12 23:09:31.192 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:31.192 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:31.192 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.193 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 3690.563421 ms
06-12 23:09:31.193 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:31.193 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:31.196 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:31.196 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:31.196 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:31.197 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:31.197 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:31.197 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:31.198 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.1757752001285553, threshold=0.9, point=(44.0, 312.0)
06-12 23:09:31.198 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.1757752001285553 < threshold=0.9
06-12 23:09:31.198 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 3695.460613 ms
06-12 23:09:31.249 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744571249
06-12 23:09:31.249 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:31.249 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744571249
06-12 23:09:31.249 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:31.249 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:31.249 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.249 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 3746.613036 ms
06-12 23:09:31.249 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744571249
06-12 23:09:31.249 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:31.249 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:31.249 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744571249
06-12 23:09:31.249 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:31.249 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:31.249 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.250 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 3747.208651 ms
06-12 23:09:31.250 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:31.250 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:31.253 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:31.253 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:31.253 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:31.253 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:31.253 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:31.253 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:31.254 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.14264409244060516, threshold=0.9, point=(43.0, 312.0)
06-12 23:09:31.254 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.14264409244060516 >= threshold=0.9? false, brightness=248.05133928571428 < 100? false
06-12 23:09:31.254 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 3751.940382 ms
06-12 23:09:31.305 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744571305
06-12 23:09:31.305 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:31.305 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744571305
06-12 23:09:31.305 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:31.305 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:31.305 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.305 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:31.305 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:31.305 D/NaApp   (10049): Template cache size: 27
06-12 23:09:31.305 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:31.400 D/NaApp   (10049): Avail: 2253MB | Total: 3558MB
06-12 23:09:31.400 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:31.400 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:31.400 D/NaApp   (10049): compareListImage started iteration 9 at 3897.444074 ms
06-12 23:09:31.452 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:31.453 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:31.454 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:31.455 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:31.455 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:31.455 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:31.575 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:31.577 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:31.587 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:31.596 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:31.597 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:31.600 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:31.603 D/NaApp   (10049): grabScreenshot completed in 202.445962 ms
06-12 23:09:31.704 D/NaApp   (10049): Avail: 2246MB | Total: 3558MB
06-12 23:09:31.704 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:31.704 D/NaApp   (10049): templatePaths.forEach started at 4201.978843 ms
06-12 23:09:31.704 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:31.704 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4202.087228 ms
06-12 23:09:31.705 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744571704
06-12 23:09:31.705 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:31.705 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:31.705 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744571704
06-12 23:09:31.705 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:31.705 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:31.705 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.705 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4202.592036 ms
06-12 23:09:31.705 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:31.705 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:31.705 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:31.705 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:31.706 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.1757752001285553, threshold=0.9, point=(44.0, 312.0)
06-12 23:09:31.706 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.1757752001285553 < threshold=0.9
06-12 23:09:31.706 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4203.698536 ms
06-12 23:09:31.764 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744571764
06-12 23:09:31.764 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:31.764 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744571764
06-12 23:09:31.764 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:31.764 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:31.764 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.764 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4262.088613 ms
06-12 23:09:31.765 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744571764
06-12 23:09:31.765 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:31.765 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:31.765 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744571764
06-12 23:09:31.765 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:31.765 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:31.765 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.765 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4262.656036 ms
06-12 23:09:31.765 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:31.765 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:31.765 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:31.765 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:31.766 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.14264409244060516, threshold=0.9, point=(43.0, 312.0)
06-12 23:09:31.766 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.14264409244060516 >= threshold=0.9? false, brightness=248.05133928571428 < 100? false
06-12 23:09:31.766 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4263.773843 ms
06-12 23:09:31.830 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744571830
06-12 23:09:31.830 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:31.830 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744571830
06-12 23:09:31.830 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:31.830 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:31.830 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:31.830 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:31.830 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:31.830 D/NaApp   (10049): Template cache size: 27
06-12 23:09:31.830 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:31.923 D/NaApp   (10049): Avail: 2179MB | Total: 3558MB
06-12 23:09:31.923 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:31.923 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:31.923 D/NaApp   (10049): compareListImage started iteration 10 at 4420.32592 ms
06-12 23:09:31.974 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:31.975 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:31.975 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:31.975 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:31.975 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:31.975 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:31.990 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:31.990 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:31.992 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:32.008 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:32.008 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:32.011 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:32.013 D/NaApp   (10049): grabScreenshot completed in 89.727615 ms
06-12 23:09:32.116 D/NaApp   (10049): Avail: 2147MB | Total: 3558MB
06-12 23:09:32.116 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:32.116 D/NaApp   (10049): templatePaths.forEach started at 4613.555882 ms
06-12 23:09:32.116 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:32.116 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 4613.695343 ms
06-12 23:09:32.116 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744572116
06-12 23:09:32.116 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:32.116 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:32.116 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744572116
06-12 23:09:32.116 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:32.116 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:32.116 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.117 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 4614.366497 ms
06-12 23:09:32.117 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:32.117 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:32.120 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:32.120 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:32.120 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:32.122 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:32.122 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:32.122 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:32.123 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.23638243973255157, threshold=0.9, point=(44.0, 314.0)
06-12 23:09:32.123 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.23638243973255157 < threshold=0.9
06-12 23:09:32.123 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 4620.330074 ms
06-12 23:09:32.178 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744572178
06-12 23:09:32.179 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:32.179 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744572178
06-12 23:09:32.179 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:32.179 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:32.179 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.179 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 4676.372266 ms
06-12 23:09:32.179 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744572179
06-12 23:09:32.179 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:32.179 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:32.179 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744572179
06-12 23:09:32.179 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:32.179 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:32.179 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.180 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 4677.87942 ms
06-12 23:09:32.180 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:32.180 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:32.190 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:32.190 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:32.190 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:32.191 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:32.191 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:32.191 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:32.192 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.18491624295711517, threshold=0.9, point=(43.0, 312.0)
06-12 23:09:32.193 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.18491624295711517 >= threshold=0.9? false, brightness=248.49888392857142 < 100? false
06-12 23:09:32.193 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 4690.338112 ms
06-12 23:09:32.248 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744572247
06-12 23:09:32.248 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:32.248 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744572247
06-12 23:09:32.248 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:32.248 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:32.248 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.248 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:32.248 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:32.248 D/NaApp   (10049): Template cache size: 27
06-12 23:09:32.248 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:32.372 D/NaApp   (10049): Avail: 2125MB | Total: 3558MB
06-12 23:09:32.372 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:32.372 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:32.372 D/NaApp   (10049): compareListImage started iteration 11 at 4869.657997 ms
06-12 23:09:32.448 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:32.448 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:32.448 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:32.448 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:32.448 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:32.448 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:32.455 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:32.456 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:32.458 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:32.468 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:32.468 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:32.471 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:32.474 D/NaApp   (10049): grabScreenshot completed in 101.139692 ms
06-12 23:09:32.648 D/NaApp   (10049): Avail: 2094MB | Total: 3558MB
06-12 23:09:32.648 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:32.648 D/NaApp   (10049): templatePaths.forEach started at 5145.64742 ms
06-12 23:09:32.648 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:32.648 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5145.799112 ms
06-12 23:09:32.648 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744572648
06-12 23:09:32.649 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:32.649 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:32.649 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744572648
06-12 23:09:32.649 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:32.649 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:32.649 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.649 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5146.90365 ms
06-12 23:09:32.649 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:32.650 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:32.650 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:32.650 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:32.651 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.26569831371307373, threshold=0.9, point=(44.0, 314.0)
06-12 23:09:32.651 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.26569831371307373 < threshold=0.9
06-12 23:09:32.651 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5148.351727 ms
06-12 23:09:32.712 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744572711
06-12 23:09:32.712 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:32.712 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744572711
06-12 23:09:32.712 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:32.712 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:32.712 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.712 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5209.597266 ms
06-12 23:09:32.712 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744572712
06-12 23:09:32.712 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:32.712 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:32.712 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744572712
06-12 23:09:32.712 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:32.712 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:32.712 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.713 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5210.247612 ms
06-12 23:09:32.713 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:32.713 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:32.713 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:32.713 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:32.714 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.20533861219882965, threshold=0.9, point=(43.0, 314.0)
06-12 23:09:32.714 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.20533861219882965 >= threshold=0.9? false, brightness=248.80580357142856 < 100? false
06-12 23:09:32.714 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5211.72765 ms
06-12 23:09:32.768 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744572768
06-12 23:09:32.768 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:32.768 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744572768
06-12 23:09:32.768 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:32.768 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:32.768 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:32.768 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:32.768 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:32.768 D/NaApp   (10049): Template cache size: 27
06-12 23:09:32.768 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:32.883 D/NaApp   (10049): Avail: 2055MB | Total: 3558MB
06-12 23:09:32.883 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:32.883 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:32.883 D/NaApp   (10049): compareListImage started iteration 12 at 5380.478727 ms
06-12 23:09:32.936 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:32.937 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:32.937 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:32.937 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:32.937 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:32.937 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:32.940 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:32.940 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:32.943 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:32.949 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:32.949 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:32.949 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:32.951 D/NaApp   (10049): grabScreenshot completed in 67.526616 ms
06-12 23:09:33.031 D/NaApp   (10049): Avail: 2028MB | Total: 3558MB
06-12 23:09:33.031 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 34 MB
06-12 23:09:33.031 D/NaApp   (10049): templatePaths.forEach started at 5528.78915 ms
06-12 23:09:33.031 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:33.031 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5528.94465 ms
06-12 23:09:33.031 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744573031
06-12 23:09:33.031 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:33.032 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:33.032 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744573031
06-12 23:09:33.032 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:33.032 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:33.032 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.032 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5529.842381 ms
06-12 23:09:33.032 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home.png
06-12 23:09:33.032 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:33.036 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home.png (30x51)
06-12 23:09:33.036 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home.png, size=30x51
06-12 23:09:33.036 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home.png, original size=30x51, scaled size=15x25, scale=0.5
06-12 23:09:33.036 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:33.036 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:33.037 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:33.037 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.2841670513153076, threshold=0.9, point=(44.0, 314.0)
06-12 23:09:33.037 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.2841670513153076 < threshold=0.9
06-12 23:09:33.038 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5535.169881 ms
06-12 23:09:33.090 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744573090
06-12 23:09:33.090 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:33.090 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744573090
06-12 23:09:33.090 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:33.090 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:33.090 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.090 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5587.665419 ms
06-12 23:09:33.090 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744573090
06-12 23:09:33.090 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:33.090 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:33.090 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744573090
06-12 23:09:33.090 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:33.090 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:33.090 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.091 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5588.308919 ms
06-12 23:09:33.091 D/NaApp   (10049): Removed empty template from cache: Img/SMG935F/home_gray.png
06-12 23:09:33.091 D/NaApp   (10049): Cache miss, loading: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:33.095 D/NaApp   (10049): Template pre-resized for 640x360: Img/SMG935F/home_gray.png (33x50)
06-12 23:09:33.095 D/NaApp   (10049): Template loaded using global resources: Img/SMG935F/home_gray.png, size=33x50
06-12 23:09:33.095 D/NaApp   (10049): Template scaled using global resources: Img/SMG935F/home_gray.png, original size=33x50, scaled size=16x25, scale=0.5
06-12 23:09:33.096 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:33.096 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:33.096 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:33.097 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.24701227247714996, threshold=0.9, point=(43.0, 314.0)
06-12 23:09:33.097 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.24701227247714996 >= threshold=0.9? false, brightness=247.2220982142857 < 100? false
06-12 23:09:33.097 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5594.489689 ms
06-12 23:09:33.148 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744573148
06-12 23:09:33.148 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:33.148 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744573148
06-12 23:09:33.148 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:33.148 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:33.148 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.148 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:33.148 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:33.148 D/NaApp   (10049): Template cache size: 27
06-12 23:09:33.148 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:33.240 D/NaApp   (10049): Avail: 1996MB | Total: 3558MB
06-12 23:09:33.240 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:33.240 D/NaApp   (10049): === SYSTEM HEALTH CHECK BEFORE PROCESSING ===
06-12 23:09:33.240 D/NaApp   (10049): compareListImage started iteration 13 at 5737.831419 ms
06-12 23:09:33.291 D/NaApp   (10049): 🔄 Sequential processing 2 templates
06-12 23:09:33.292 D/NaApp   (10049): getSourceBitmap: Starting controlled capture 640x360
06-12 23:09:33.292 D/NaApp   (10049): VirtualDisplay already persistent and active
06-12 23:09:33.292 D/NaApp   (10049): Setting up controlled capture callback
06-12 23:09:33.292 D/NaApp   (10049): Triggering single screen capture
06-12 23:09:33.292 D/NaApp   (10049): Requesting frame from VirtualDisplay surface
06-12 23:09:33.308 D/NaApp   (10049): Processing expected ImageReader callback
06-12 23:09:33.308 D/NaApp   (10049): Converting captured image to bitmap
06-12 23:09:33.311 W/NaApp   (10049): ⚠️ Cannot get dimensions of recycled or null bitmap
06-12 23:09:33.321 D/NaApp   (10049): Successfully created bitmap: 640x360
06-12 23:09:33.321 D/NaApp   (10049): Controlled capture completed: true
06-12 23:09:33.322 D/NaApp   (10049): Successfully captured bitmap using hybrid VirtualDisplay approach
06-12 23:09:33.322 D/NaApp   (10049): grabScreenshot completed in 81.935731 ms
06-12 23:09:33.399 D/NaApp   (10049): Avail: 1969MB | Total: 3558MB
06-12 23:09:33.399 D/NaApp   (10049): App Pss: 65 MB | Private Dirty: 33 MB
06-12 23:09:33.399 D/NaApp   (10049): templatePaths.forEach started at 5896.878881 ms
06-12 23:09:33.399 D/NaApp   (10049): Processing 2 templates (limited to 3)
06-12 23:09:33.399 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home.png 5896.975688 ms
06-12 23:09:33.399 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744573399
06-12 23:09:33.399 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:33.399 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:33.399 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744573399
06-12 23:09:33.399 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:33.400 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:33.400 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.400 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home.png using slot 0 5897.518919 ms
06-12 23:09:33.400 D/NaApp   (10049): Cache hit: Img/SMG935F/home.png (scale: 0.5)
06-12 23:09:33.400 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:33.400 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:33.400 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home.png - scaledRoiMat size=28x32 (28x32), template size=15x25 (15x25), scale=0.5, gray=false
06-12 23:09:33.401 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home.png, maxVal=0.31191882491111755, threshold=0.9, point=(44.0, 314.0)
06-12 23:09:33.401 D/NaApp   (10049): ❌ FAIL: Template match below threshold. Img/SMG935F/home.png maxVal=0.31191882491111755 < threshold=0.9
06-12 23:09:33.401 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home.png slot 0 5898.59615 ms
06-12 23:09:33.452 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744573452
06-12 23:09:33.452 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:33.452 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744573452
06-12 23:09:33.452 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:33.452 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:33.452 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.452 D/NaApp   (10049): templatePaths.forEach Img/SMG935F/home_gray.png 5949.699573 ms
06-12 23:09:33.452 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Attempting to acquire slot at 1749744573452
06-12 23:09:33.452 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:899
06-12 23:09:33.452 D/NaApp   (10049): 🔍 GLOBAL MAT ACQUIRE: Active slots before: 0/3
06-12 23:09:33.452 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Resource slot 0 acquired successfully at 1749744573452
06-12 23:09:33.452 D/NaApp   (10049): ✅ GLOBAL MAT ACQUIRE: Active slots after: 1/3
06-12 23:09:33.452 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat objects available: screen=true, roi=true, result=true
06-12 23:09:33.452 D/NaApp   (10049): 🔧 GLOBAL MAT ACQUIRE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.453 D/NaApp   (10049): templatePaths.forEach matching Img/SMG935F/home_gray.png using slot 0 5950.152919 ms
06-12 23:09:33.453 D/NaApp   (10049): Cache hit: Img/SMG935F/home_gray.png (scale: 0.5)
06-12 23:09:33.453 D/NaApp   (10049): Original ROI extracted slot 0: Img/SMG935F/home_gray.png - original rect={3, 285, 57x65}, valid rect={3, 285, 57x65}, roiMat size=57x65 (57x65)
06-12 23:09:33.453 D/NaApp   (10049): ROI scaling slot 0: Img/SMG935F/home_gray.png - original size=57x65 (57x65), scale=0.5, newSize=28x32 (28x32)
06-12 23:09:33.453 D/NaApp   (10049): Template matching dimensions slot 0: Img/SMG935F/home_gray.png - scaledRoiMat size=28x32 (28x32), template size=16x25 (16x25), scale=0.5, gray=false
06-12 23:09:33.453 D/NaApp   (10049): Template match slot 0: path=Img/SMG935F/home_gray.png, maxVal=0.2450796514749527, threshold=0.9, point=(43.0, 312.0)
06-12 23:09:33.453 D/NaApp   (10049): ❌ FAIL: Template match failed. Img/SMG935F/home_gray.png maxVal=0.2450796514749527 >= threshold=0.9? false, brightness=245.8861607142857 < 100? false
06-12 23:09:33.453 D/NaApp   (10049): templatePaths.forEach matching end Img/SMG935F/home_gray.png slot 0 5951.029765 ms
06-12 23:09:33.504 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Attempting to release slot 0 at 1749744573504
06-12 23:09:33.504 D/NaApp   (10049): 🔍 GLOBAL MAT RELEASE: Called from: com.dragon.clicker.service.OpencvService$compareListImagesSequential$2.invokeSuspend:945
06-12 23:09:33.504 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Resource slot 0 released successfully at 1749744573504
06-12 23:09:33.504 D/NaApp   (10049): ✅ GLOBAL MAT RELEASE: Active slots: 1 -> 0
06-12 23:09:33.504 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat objects status: screen=true, roi=true
06-12 23:09:33.504 D/NaApp   (10049): 🔧 GLOBAL MAT RELEASE: Slot 0 - Mat native addresses: screen=512701826592, roi=512701826688
06-12 23:09:33.504 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:33.504 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:33.504 D/NaApp   (10049): Template cache size: 27
06-12 23:09:33.504 D/NaApp   (10049): compareListImage cleanup completed
06-12 23:09:33.504 D/NaApp   (10049): Starting cleanup in compareImagesLong
06-12 23:09:33.504 D/NaApp   (10049): Global template Mat address: 512755225408
06-12 23:09:33.504 D/NaApp   (10049): Active resource slots: 0/3
06-12 23:09:33.504 D/NaApp   (10049): Template cache size: 27
06-12 23:09:33.504 D/NaApp   (10049): compareImagesLong cleanup completed
06-12 23:09:33.504 D/NaApp   (10049): compareImagesLong result at 6002.120073 ms: FAILED
06-12 23:09:33.505 D/NaApp   (10049): checkLoadGame: compareImagesLong completed in 6002.184304 ms
06-12 23:09:33.505 D/NaApp   (10049): Check load game: (FAILED, null)
06-12 23:09:33.505 E/NaApp   (10049): Check failed: (FAILED, null)
06-12 23:09:33.505 E/NaApp   (10049): Fail Farm 4
06-12 23:09:33.505 D/NaApp   (10049): Automation cycle completed
06-12 23:09:33.693 D/NaApp   (10049): Auto index: 3
06-12 23:09:33.792 D/NaApp   (10049): Avail: 1904MB | Total: 3558MB
06-12 23:09:33.792 D/NaApp   (10049): App Pss: 64 MB | Private Dirty: 33 MB
06-12 23:09:34.154 D/NaApp   (10049): CPU Usage: 49%
06-12 23:09:34.159 D/NaApp   (10049): Folder already exists: /storage/emulated/0/Dragon/Farm
06-12 23:09:34.160 W/NaApp   (10049): Go Home
06-12 23:09:35.041 D/NaApp   (10049): Command success: am force-stop com.supercell.hayday
06-12 23:09:35.111 D/NaApp   (10049): Command success: rm -rf /data/data/com.supercell.hayday/cache/*
06-12 23:09:35.212 D/NaApp   (10049): Command success: service call SurfaceFlinger 1008 i32 0
06-12 23:09:35.271 D/NaApp   (10049): Command success: sync
06-12 23:09:35.551 D/NaApp   (10049): Command success: echo 1 > /proc/sys/vm/compact_memory
06-12 23:09:35.786 D/NaApp   (10049): Command success: echo 3 > /proc/sys/vm/drop_caches
06-12 23:09:35.867 D/NaApp   (10049): Command success: pm trim-caches 100M
06-12 23:09:35.917 D/NaApp   (10049): Command success: am kill-all
06-12 23:09:35.917 D/NaApp   (10049): Killed com.supercell.hayday
06-12 23:09:37.317 D/NaApp   (10049): Hayday button clicked, isRunning=true
06-12 23:09:37.317 D/NaApp   (10049): Hayday button clicked, isRunning=true
06-12 23:09:37.330 D/NaApp   (10049): Found enabled accessibility service: com.dragon.clicker.service.MyAccessibilityServiceRoot
06-12 23:09:37.330 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:09:37.330 D/NaApp   (10049): Accessibility service enabled: true
06-12 23:09:37.496 D/NaApp   (10049): Successfully applied Farm #4
06-12 23:09:37.497 E/NaApp   (10049): Error in tutorial
06-12 23:09:37.497 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@6815f4
06-12 23:09:37.499 E/NaApp   (10049): Exception in HaydayService
06-12 23:09:37.499 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@6815f4
06-12 23:09:37.500 E/NaApp   (10049): Automation error
06-12 23:09:37.500 E/NaApp   (10049): kotlinx.coroutines.JobCancellationException: Job was cancelled; job=SupervisorJobImpl{Cancelling}@6815f4
