<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dragon.clicker">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- android:dataExtractionRules="@xml/data_extraction_rules" android:fullBackupContent="@xml/backup_rules" -->
    <application
        android:name=".ClickerApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppCompat">

        <!-- Main launcher Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Clicker"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Services -->
        <service
            android:name=".service.FloatingCrosshairService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <service
            android:name=".service.OpencvService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <service
            android:name=".service.MyAccessibilityServiceRoot"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <service
            android:name=".service.HaydayForegroundService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            tools:ignore="ForegroundServicePermission" />

        <service
            android:name=".service.FloatingOverlayService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <service
            android:name=".service.EventService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection"
            tools:ignore="ForegroundServicePermission" />

        <!-- BootReceiver to start EventService on boot -->
        <receiver
            android:name=".service.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".service.RebootSimulatorReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.dragon.clicker.ACTION_REBOOT" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
